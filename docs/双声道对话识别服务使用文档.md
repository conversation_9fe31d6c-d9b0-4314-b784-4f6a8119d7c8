# 双声道对话识别服务使用文档

## 概述

双声道对话识别服务是基于现有IAT语音识别服务构建的高级功能，专门用于处理双声道音频中的对话场景。该服务能够：

1. **自动分离双声道音频**：将立体声音频分离为左右两个单声道
2. **并行语音识别**：同时对两个声道进行语音识别
3. **时间轴对齐**：按时间顺序整理识别结果
4. **智能对话分析**：识别问答模式，标记对话类型
5. **结构化输出**：生成完整的对话轮次数据

## 核心架构

```
双声道音频输入
    ↓
音频预处理与验证
    ↓
声道分离 (AudioChannelSeparationService)
    ↓
并行语音识别 (IatServiceFacade)
    ↓
时间轴对齐 (DialogueTimelineAlignmentService)
    ↓
对话模式分析
    ↓
结构化结果输出
```

## API 接口

### 1. 双声道对话识别

**接口地址：** `POST /api/iat/dialogue/recognize`

**请求参数：**
- `Content-Type: application/octet-stream`
- 请求体：双声道音频数据（字节流）

**查询参数：**
```
rate: 采样率 (默认: 16k)
aue: 音频编码格式 (默认: raw)
rst: 返回结果格式 (默认: json)
bit: 音频位深 (默认: 16)
denoiseEnable: 是否启用降噪 (默认: 1)
dwa: 是否获取中间结果 (默认: wpgs)
pgsMode: PGS模式 (默认: 1)
eos: VAD后断点值毫秒 (默认: 3000)
hotword: 热词配置 (可选)
minResponseInterval: 最小回应时间间隔毫秒 (默认: 200)
maxResponseInterval: 最大回应时间间隔毫秒 (默认: 3000)
minSpeechDuration: 最小发言持续时间毫秒 (默认: 500)
channelSwitchTolerance: 声道切换容忍时间毫秒 (默认: 100)
enableSmartSegmentation: 是否启用智能对话分段 (默认: true)
```

**响应格式：**
```json
{
  "sessionId": "uuid-string",
  "errCode": 0,
  "errMsg": null,
  "finished": true,
  "dialogueTurns": [
    {
      "turnNumber": 1,
      "speakerId": "SPEAKER_A",
      "channel": "LEFT",
      "text": "你好，我想问一下这个东西应该怎么做好的",
      "startTime": 89,
      "endTime": 885,
      "duration": 796,
      "confidence": 0.8,
      "turnType": "QUESTION",
      "wordTimings": [
        {
          "word": "你好",
          "startTime": 89,
          "endTime": 177,
          "confidence": 0.9
        }
      ]
    },
    {
      "turnNumber": 2,
      "speakerId": "SPEAKER_B",
      "channel": "RIGHT",
      "text": "噢，好的知道了",
      "startTime": 1041,
      "endTime": 1217,
      "duration": 176,
      "confidence": 0.85,
      "turnType": "ANSWER",
      "wordTimings": []
    }
  ],
  "rawData": {
    "leftChannelResults": [...],
    "rightChannelResults": [...]
  },
  "stats": {
    "totalProcessingTime": 2500,
    "totalAudioDuration": 1366,
    "leftChannelSegments": 12,
    "rightChannelSegments": 8,
    "totalTurns": 2,
    "averageResponseTime": 156.0
  }
}
```

### 2. 查询识别状态

**接口地址：** `GET /api/iat/dialogue/status/{sessionId}`

**响应格式：**
```json
{
  "sessionId": "uuid-string",
  "status": "COMPLETED",
  "message": "识别已完成",
  "timestamp": 1672531200000
}
```

## 使用示例

### Java 客户端示例

```java
@RestController
public class DialogueTestController {
    
    @Autowired
    private DialogueRecognitionService dialogueRecognitionService;
    
    @PostMapping("/test-dialogue")
    public CompletableFuture<DialogueRecognitionResult> testDialogue(
            @RequestParam("audioFile") MultipartFile audioFile) throws IOException {
        
        // 构建请求
        DialogueRecognitionRequest request = DialogueRecognitionRequest.builder()
                .sessionId(UUID.randomUUID().toString())
                .stereoAudioData(audioFile.getBytes())
                .rate("16k")
                .aue("raw")
                .rst("json")
                .alignmentConfig(
                    DialogueRecognitionRequest.DialogueAlignmentConfig.builder()
                        .minResponseInterval(200L)
                        .maxResponseInterval(3000L)
                        .minSpeechDuration(500L)
                        .enableSmartSegmentation(true)
                        .build()
                )
                .build();
        
        return dialogueRecognitionService.recognizeDialogue(request);
    }
}
```

### cURL 示例

```bash
curl -X POST "http://localhost:8080/api/iat/dialogue/recognize" \
  -H "Content-Type: application/octet-stream" \
  -d @stereo_audio.wav \
  --data-urlencode "rate=16k" \
  --data-urlencode "minResponseInterval=200" \
  --data-urlencode "maxResponseInterval=3000"
```

## 配置说明

### 音频格式要求

- **声道数：** 必须是双声道（立体声）
- **采样率：** 支持 8k, 16k, 22k, 44k, 48k
- **位深度：** 支持 16位
- **编码格式：** 支持 PCM raw 格式

### 对话对齐参数

- **minResponseInterval：** 最小回应时间间隔，用于判断是否为有效回应
- **maxResponseInterval：** 最大回应时间间隔，超过此时间认为不是直接回应
- **minSpeechDuration：** 最小发言持续时间，过短的片段会被过滤
- **channelSwitchTolerance：** 同一说话人连续发言的时间容忍度
- **enableSmartSegmentation：** 是否启用智能对话分段

## 对话类型说明

- **QUESTION：** 问题类型，包含疑问词或疑问语气
- **ANSWER：** 回答类型，紧跟在问题后的不同说话人发言
- **STATEMENT：** 陈述类型，一般性陈述内容
- **INTERRUPTION：** 插话类型，打断对方发言

## 性能优化建议

1. **音频预处理：** 确保音频质量，减少噪音干扰
2. **参数调优：** 根据实际对话场景调整时间间隔参数
3. **热词配置：** 针对特定领域配置热词提高识别准确率
4. **并发控制：** 合理控制并发请求数量，避免资源过载

## 错误处理

常见错误码：
- `0`：成功
- `-1`：通用错误
- `10001`：音频格式错误
- `10002`：声道分离失败
- `10003`：语音识别失败
- `10004`：时间轴对齐失败

## 注意事项

1. **音频质量：** 双声道音频需要确保左右声道分别对应不同说话人
2. **时间同步：** 确保双声道音频时间同步，避免时间偏移
3. **资源消耗：** 双声道并行识别会消耗更多计算资源
4. **结果延迟：** 完整的对话分析需要等待所有识别完成，可能有一定延迟

## 扩展功能

未来可以扩展的功能：
- 多声道支持（超过2个声道）
- 说话人识别和聚类
- 情感分析
- 对话摘要生成
- 实时流式对话识别
