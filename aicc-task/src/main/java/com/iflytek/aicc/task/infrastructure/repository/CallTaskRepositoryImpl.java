package com.iflytek.aicc.task.infrastructure.repository;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.iflytek.aicc.common.utils.MongoCryptoUtils;
import com.iflytek.aicc.task.common.enums.CallTaskStatusEnum;
import com.iflytek.aicc.task.domain.bo.CallTaskListBO;
import com.iflytek.aicc.task.domain.entity.CallTaskEntity;
import com.iflytek.aicc.task.domain.repository.CallTaskRepository;
import com.iflytek.aicc.task.infrastructure.repository.conveter.CallTaskConvertorInfra;
import com.iflytek.aicc.task.infrastructure.repository.dataobject.CallTaskDO;
import com.iflytek.aicc.task.infrastructure.repository.dataobject.CallerUsedDO;
import com.iflytek.aicc.task.infrastructure.repository.dataobject.TaskPoolGatherDO;
import com.iflytek.aicc.task.infrastructure.repository.mapper.CallTaskMapper;
import com.iflytek.outbound.utils.PageUtil;
import com.iflytek.outbound.vo.Page;
import com.iflytek.outbound.vo.PageList;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 主叫号码
 *
 * <AUTHOR>
 * @date 2023-08-09
 **/
@Service
public class CallTaskRepositoryImpl implements CallTaskRepository {

    private final CallTaskMapper callTaskMapper;
    private final MongoCryptoUtils mongoCryptoUtils;

    public CallTaskRepositoryImpl(CallTaskMapper callTaskMapper, MongoCryptoUtils mongoCryptoUtils) {
        this.callTaskMapper = callTaskMapper;
        this.mongoCryptoUtils = mongoCryptoUtils;
    }


    @Override
    public void batchInsert(List<CallTaskEntity> callTaskList) {
        List<CallTaskDO> callTaskDOList = CallTaskConvertorInfra.toDOList(callTaskList);
        Lists.partition(callTaskDOList, 1000).forEach(callTaskMapper::batchInsert);
    }

    @Override
    public CallTaskEntity getCallTaskById(Long id) {
        LambdaQueryWrapper<CallTaskDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CallTaskDO::getId, id);
        queryWrapper.eq(CallTaskDO::getDeleted, 0);
        CallTaskDO callTaskDO = callTaskMapper.selectOne(queryWrapper);
        return CallTaskConvertorInfra.toEntity(callTaskDO);
    }

    @Override
    public int deleteByQueueIds(List<Long> queueIds) {
        LambdaUpdateWrapper<CallTaskDO> wrapper = new LambdaUpdateWrapper<CallTaskDO>()
                .in(CallTaskDO::getQueueId, queueIds).eq(CallTaskDO::getDeleted, 0).eq(CallTaskDO::getStatus, 0)
                .set(CallTaskDO::getDeleted, 1)
                .set(CallTaskDO::getUpdatedTime, new Date());
        return callTaskMapper.update(null, wrapper);
    }

    @Override
    public int deleteByTaskId(Long id, String loginUserId) {
        LambdaUpdateWrapper<CallTaskDO> wrapper = new LambdaUpdateWrapper<CallTaskDO>()
                .eq(CallTaskDO::getId, id).eq(CallTaskDO::getDeleted, 0).eq(CallTaskDO::getStatus, 0)
                .set(CallTaskDO::getDeleted, 1)
                .set(CallTaskDO::getUpdatedTime, new Date())
                .set(CallTaskDO::getUpdatedBy, loginUserId);
        return callTaskMapper.update(null, wrapper);
    }

    @Override
    public int modifyStatus(Long taskId, Integer status, String statusDesc, Integer revision, String permitId) {
        LambdaUpdateWrapper<CallTaskDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CallTaskDO::getDeleted, 0).eq(CallTaskDO::getId, taskId);
        wrapper.set(CallTaskDO::getStatus, status);
        wrapper.set(CallTaskDO::getStatusDesc, statusDesc);
        if (revision != null) {
            wrapper.eq(CallTaskDO::getRevision, revision);
            wrapper.set(CallTaskDO::getRevision, revision + 1);
        }
        if (StringUtils.isNotBlank(permitId)) {
            wrapper.set(CallTaskDO::getPermitId, permitId);
        }
        if (CallTaskStatusEnum.IN_CALL.getCode().equals(status)) {
            wrapper.set(CallTaskDO::getSendTime, new Date());
        }
        wrapper.set(CallTaskDO::getUpdatedTime, new Date());
        return callTaskMapper.update(null, wrapper);
    }

    @Override
    public List<CallTaskEntity> getUnCalledTask(Integer waitTime) {
        LambdaQueryWrapper<CallTaskDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CallTaskDO::getDeleted, 0).eq(CallTaskDO::getStatus, 0);
        Date endTime = DateUtil.offsetMinute(new Date(), -waitTime);
        Date beginDate = DateUtil.yesterday();
        queryWrapper.between(CallTaskDO::getTaskTime, beginDate, endTime);
        queryWrapper.orderByAsc(CallTaskDO::getTaskTime);
        List<CallTaskDO> callTaskDOList = callTaskMapper.selectList(queryWrapper);
        return CallTaskConvertorInfra.toEntityList(callTaskDOList);

    }

    @Override
    public List<CallTaskEntity> getInCallTask(Integer waitTime) {
        LambdaQueryWrapper<CallTaskDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CallTaskDO::getDeleted, 0).eq(CallTaskDO::getStatus, 1);
        Date endTime = DateUtil.offsetMinute(new Date(), -waitTime);
        Date beginDate = DateUtil.yesterday();
        queryWrapper.between(CallTaskDO::getSendTime, beginDate, endTime);
        queryWrapper.orderByAsc(CallTaskDO::getSendTime);
        List<CallTaskDO> callTaskDOList = callTaskMapper.selectList(queryWrapper);
        return CallTaskConvertorInfra.toEntityList(callTaskDOList);
    }

    @Override
    public List<CallTaskEntity> getBeforeUnCalledTask() {
        LambdaQueryWrapper<CallTaskDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CallTaskDO::getDeleted, 0).eq(CallTaskDO::getStatus, 0);
        Date start = new DateTime().plusDays(-1).withHourOfDay(0).withMillisOfDay(0).withSecondOfMinute(0).toDate();
        Date end = new DateTime().plusDays(-1).withHourOfDay(23).withMinuteOfHour(59).withSecondOfMinute(59).toDate();
        queryWrapper.between(CallTaskDO::getTaskTime, start, end);
        List<CallTaskDO> callTaskDOList = callTaskMapper.selectList(queryWrapper);
        return CallTaskConvertorInfra.toEntityList(callTaskDOList);
    }

    @Override
    public List<CallTaskEntity> getNotStartCallTaskByIds(List<Long> taskIds) {
        LambdaQueryWrapper<CallTaskDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CallTaskDO::getId, taskIds);
        queryWrapper.eq(CallTaskDO::getDeleted, 0);
        queryWrapper.in(CallTaskDO::getStatus, CallTaskStatusEnum.NOT_CALLED.getCode());
        List<CallTaskDO> callTaskDO = callTaskMapper.selectList(queryWrapper);
        return CallTaskConvertorInfra.toEntityList(callTaskDO);
    }

    @Override
    public List<CallTaskEntity> getUnCompleteCallTaskByIds(List<Long> taskIds) {
        LambdaQueryWrapper<CallTaskDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CallTaskDO::getId, taskIds);
        queryWrapper.eq(CallTaskDO::getDeleted, 0);
        queryWrapper.in(CallTaskDO::getStatus, Arrays.asList(CallTaskStatusEnum.NOT_CALLED.getCode(), CallTaskStatusEnum.IN_CALL.getCode()));
        List<CallTaskDO> callTaskDO = callTaskMapper.selectList(queryWrapper);
        return CallTaskConvertorInfra.toEntityList(callTaskDO);
    }

    @Override
    public PageList<CallTaskEntity> getCallTaskByPoolId(CallTaskListBO callTaskListDto, Page page) {
        LambdaQueryWrapper<CallTaskDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CallTaskDO::getDeleted, 0);
        queryWrapper.eq(CallTaskDO::getTaskPoolId, callTaskListDto.getTaskPoolId());
        queryWrapper.eq(CallTaskDO::getStatus, callTaskListDto.getStatus());
        queryWrapper.le(CallTaskDO::getTaskTime, callTaskListDto.getCreatedEndTime());
        queryWrapper.ge(CallTaskDO::getTaskTime, callTaskListDto.getCreatedStartTime());
        if (callTaskListDto.getExecuteStartTime() != null && callTaskListDto.getExecuteEndTime() != null) {
            queryWrapper.le(CallTaskDO::getSendTime, callTaskListDto.getExecuteStartTime());
            queryWrapper.ge(CallTaskDO::getSendTime, callTaskListDto.getExecuteEndTime());
        }
        String callee = callTaskListDto.getCallee();
        if (StringUtils.isNotBlank(callTaskListDto.getCallee())) {
            callee = mongoCryptoUtils.enCryptStr("callee", callee);
            queryWrapper.eq(CallTaskDO::getCallee, callee);
        }
        if (StringUtils.isNotBlank(callTaskListDto.getCaller())) {
            queryWrapper.eq(CallTaskDO::getCaller, callTaskListDto.getCaller());
        }
        if (callTaskListDto.getBusinessTaskId() != null) {
            queryWrapper.eq(CallTaskDO::getBusinessTaskId, callTaskListDto.getBusinessTaskId());
        }
        if (callTaskListDto.getId() != null) {
            queryWrapper.eq(CallTaskDO::getId, callTaskListDto.getId());
        }
        queryWrapper.orderByDesc(CallTaskDO::getTaskTime);
        PageList<CallTaskDO> pageList = new PageList<>();
        if (page != null) {
            pageList = PageUtil.doPage(() -> callTaskMapper.selectList(queryWrapper), page);
        } else {
            List<CallTaskDO> callTaskDOList = callTaskMapper.selectList(queryWrapper);
            pageList.setData(callTaskDOList);
        }
        return CallTaskConvertorInfra.toPageEntityList(pageList);
    }

    /**
     * 未完成的任务
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    @Override
    public List<CallTaskEntity> queryUnfinishedTasks(Date startTime, Date endTime, Integer startIntervalTime, Integer endIntervalTime) {
        //查询 未完成的任务
        LambdaQueryWrapper<CallTaskDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (startTime == null) {
            if (startIntervalTime == null) {
                startIntervalTime = 2;
            }
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.HOUR_OF_DAY, -startIntervalTime);
            startTime = calendar.getTime();
        }
        if (endTime == null) {
            if (endIntervalTime == null) {
                endIntervalTime = 20;
            }
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.MINUTE, -endIntervalTime);
            endTime = calendar.getTime();
        }
        // 获取当前时间
        lambdaQueryWrapper.ge(CallTaskDO::getSendTime, startTime);
        lambdaQueryWrapper.le(CallTaskDO::getSendTime, endTime);
        lambdaQueryWrapper.eq(CallTaskDO::getDeleted, 0);

        //状态为呼叫中
        lambdaQueryWrapper.eq(CallTaskDO::getStatus, CallTaskStatusEnum.IN_CALL.getCode());
        lambdaQueryWrapper.orderByAsc(CallTaskDO::getSendTime);
        lambdaQueryWrapper.select(CallTaskDO::getId);
        List<CallTaskDO> list = callTaskMapper.selectList(lambdaQueryWrapper);
        return CallTaskConvertorInfra.toEntityList(list);
    }

    @Override
    public List<TaskPoolGatherDO> gatherCallTask(Date startTime, Date endTime, Long taskPoolId) {
        return callTaskMapper.gatherCallTask(startTime, endTime, taskPoolId);
    }

    @Override
    public List<CallTaskEntity> getTaskByBusinessTaskIds(List<Long> businessTaskIds) {
        LambdaQueryWrapper<CallTaskDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CallTaskDO::getDeleted, 0);
        queryWrapper.in(CallTaskDO::getBusinessTaskId, businessTaskIds);
        List<CallTaskDO> list = callTaskMapper.selectList(queryWrapper);
        return CallTaskConvertorInfra.toEntityList(list);
    }

    @Override
    public List<CallerUsedDO> queryCallerTodayUsed(Integer total) {
        Date start = new DateTime().withHourOfDay(0).withMillisOfDay(0).withSecondOfMinute(0).toDate();
        Date end = new DateTime().withHourOfDay(23).withMinuteOfHour(59).withSecondOfMinute(59).toDate();
        return callTaskMapper.queryCallerTodayUsed(start, end, total);
    }

    @Override
    public Integer getUnCalledTaskAtNight() {
        LambdaQueryWrapper<CallTaskDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CallTaskDO::getDeleted, 0).eq(CallTaskDO::getStatus, 0);
        Date start = new DateTime().withHourOfDay(0).withMillisOfDay(0).withSecondOfMinute(0).toDate();
        Date end = new DateTime().withHourOfDay(23).withMinuteOfHour(59).withSecondOfMinute(59).toDate();
        queryWrapper.between(CallTaskDO::getTaskTime, start, end);
        return callTaskMapper.selectCount(queryWrapper);
    }
}
