package com.iflytek.aicc.task.domain.repository;


import com.iflytek.aicc.task.api.vo.DictSelectVO;
import com.iflytek.aicc.task.domain.bo.SipTrunkListQueryBO;
import com.iflytek.aicc.task.domain.entity.SipTrunkEntity;
import com.iflytek.aicc.task.infrastructure.repository.dataobject.SipTrunkListDO;

import java.util.List;

/**
 * 中继管理
 *
 * <AUTHOR>
 * @date 2023/11/01
 */
public interface SipTrunkRepository {

    /**
     * 查询中继信息
     *
     * @param id 中继id
     * @return 中继信息
     */
    SipTrunkEntity getById(Long id);

    /**
     * 查询中继信息
     *
     * @param ids 中继id
     * @return 中继信息
     */
    List<SipTrunkEntity> selectList(List<Long> ids);

    /**
     * 保存中继信息
     *
     * @param sipTrunkEntity 中继信息
     */
    void insert(SipTrunkEntity sipTrunkEntity);

    /**
     * 保存中继信息
     *
     * @param sipTrunkEntity 中继信息
     */
    void update(SipTrunkEntity sipTrunkEntity);

    /**
     * 查询中继列表
     *
     * @param sipTrunkListQueryBO 查询条件
     */
    List<SipTrunkListDO> queryList(SipTrunkListQueryBO sipTrunkListQueryBO);

    void delete(String loginUserId, Long id);

    List<DictSelectVO> queryRelay();
}
