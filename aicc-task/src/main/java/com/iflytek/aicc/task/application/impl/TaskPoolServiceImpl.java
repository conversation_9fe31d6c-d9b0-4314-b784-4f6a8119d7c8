package com.iflytek.aicc.task.application.impl;

import com.iflytek.aicc.common.utils.CommonUtil;
import com.iflytek.aicc.task.application.TaskPoolService;
import com.iflytek.aicc.task.application.convertor.TaskPoolConvertorApp;
import com.iflytek.aicc.task.application.dto.PlatformBindDto;
import com.iflytek.aicc.task.application.dto.PlatformListBindingDto;
import com.iflytek.aicc.task.application.dto.TaskPoolAddDto;
import com.iflytek.aicc.task.application.dto.TaskPoolListDto;
import com.iflytek.aicc.task.application.dto.TaskPoolSaveDto;
import com.iflytek.aicc.task.application.dto.TaskPoolUpdateStatusDto;
import com.iflytek.aicc.task.application.listener.event.DeleteTaskPoolEvent;
import com.iflytek.aicc.task.application.listener.event.StartQueueConsumerEvent;
import com.iflytek.aicc.task.domain.bo.TaskPoolUpdateStatusBO;
import com.iflytek.aicc.task.domain.entity.TaskPoolEntity;
import com.iflytek.aicc.task.domain.entity.TaskPoolPlatformRelEntity;
import com.iflytek.aicc.task.domain.repository.TaskPoolPlatformRelRepository;
import com.iflytek.aicc.task.domain.repository.TaskPoolRepository;
import com.iflytek.aicc.task.infrastructure.repository.dataobject.TaskPoolListDO;
import com.iflytek.aicc.task.infrastructure.rpc.operation.OperationService;
import com.iflytek.aicc.task.infrastructure.rpc.operation.dataobject.PlatformListResultDO;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.UidService;
import com.iflytek.outbound.utils.JacksonUtils;
import com.iflytek.outbound.vo.Page;
import com.iflytek.outbound.vo.PageList;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 任务池
 *
 * <AUTHOR>
 * @date 2024/06/14
 */
@Service
public class TaskPoolServiceImpl implements TaskPoolService {
    private final TaskPoolRepository taskPoolRepository;
    private final TaskPoolPlatformRelRepository taskPoolPlatformRelRepository;
    private final ApplicationEventPublisher applicationEventPublisher;
    private final OperationService operationService;
    private final UidService uidService;

    public TaskPoolServiceImpl(TaskPoolRepository taskPoolRepository, TaskPoolPlatformRelRepository taskPoolPlatformRelRepository,
                               ApplicationEventPublisher applicationEventPublisher, OperationService operationService, UidService uidService) {
        this.taskPoolRepository = taskPoolRepository;
        this.taskPoolPlatformRelRepository = taskPoolPlatformRelRepository;
        this.applicationEventPublisher = applicationEventPublisher;
        this.operationService = operationService;
        this.uidService = uidService;
    }

    @Override
    public PageList<TaskPoolListDO> query(TaskPoolListDto taskPoolListDto, Page page) {
        return taskPoolRepository.query(taskPoolListDto.getName(), page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(TaskPoolAddDto taskPoolAddDto) {
        TaskPoolEntity taskPoolEntity = TaskPoolConvertorApp.toAddEntity(taskPoolAddDto);
        if (hasOverlap(taskPoolEntity)) {
            throw new MedicalBusinessException("时间段不允许出现重叠");
        }
        taskPoolRepository.add(taskPoolEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(TaskPoolSaveDto taskPoolSaveDto) {
        TaskPoolEntity taskPoolEntity = TaskPoolConvertorApp.toSaveEntity(taskPoolSaveDto);
        if (hasOverlap(taskPoolEntity)) {
            throw new MedicalBusinessException("时间段不允许出现重叠");
        }
        taskPoolRepository.save(taskPoolEntity);
    }

    private boolean hasOverlap(TaskPoolEntity taskPoolEntity) {
        List<TaskPoolEntity.WorkTime> workTimeList = taskPoolEntity.getWorkTimeList();
        if (CollectionUtils.isEmpty(workTimeList) || workTimeList.size() == 1) {
            return false;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        List<Map<String, LocalTime>> timePeriods = new ArrayList<>();
        for (TaskPoolEntity.WorkTime workTime : workTimeList) {
            LocalTime beginTime = LocalTime.parse(workTime.getBegin(), formatter);
            LocalTime endTime = LocalTime.parse(workTime.getEnd(), formatter);
            Map<String, LocalTime> timePeriod = new HashMap<>();
            timePeriod.put("startTime", beginTime);
            timePeriod.put("endTime", endTime);
            timePeriods.add(timePeriod);
        }
        //按开始时间顺序排列
        timePeriods.sort(Comparator.comparing(t -> t.get("startTime")));
        //首个结束时间
        LocalTime previousEndDate = timePeriods.get(0).get("endTime");
        //重新保存对象
        for (int i = 1; i < timePeriods.size(); i++) {
            Map<String, LocalTime> currentPeriod = timePeriods.get(i);
            if (!previousEndDate.isBefore(currentPeriod.get("startTime"))) {
                // 当前时间段的开始日期早于上一个时间段的结束日期，存在重叠
                return true;
            } else {
                // 相邻时间段之间没有间隔
                previousEndDate = currentPeriod.get("endTime");
            }
        }
        //保存排序后时间
        List<TaskPoolEntity.WorkTime> saveList = timePeriods.stream().map(timePeriod ->
                new TaskPoolEntity.WorkTime(timePeriod.get("startTime").format(formatter), timePeriod.get("endTime").format(formatter)))
                .collect(Collectors.toList());
        taskPoolEntity.setWorkTime(JacksonUtils.writeValueAsString(saveList));
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(String loginUserId, Long id) {
        TaskPoolEntity taskPool = taskPoolRepository.getById(id);
        if (taskPool == null) {
            return 0;
        }
        if (taskPool.getDefaultPool() == 1) {
            throw new MedicalBusinessException("默认任务池不能删除");
        }
        int delete = taskPoolRepository.delete(loginUserId, id);
        //删除逻辑需要触发后续任务清理任务逻辑事件，具体任务清理由任务管理监听处理
        applicationEventPublisher.publishEvent(new DeleteTaskPoolEvent(this, id));
        return delete;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateStatus(TaskPoolUpdateStatusDto taskPoolUpdateStatusDto) {
        TaskPoolUpdateStatusBO taskPoolUpdateStatusBO = TaskPoolConvertorApp.toUpdateStatusBO(taskPoolUpdateStatusDto);
        int count = taskPoolRepository.updateStatus(taskPoolUpdateStatusBO);
        //队列开始消费事件
        if (taskPoolUpdateStatusDto.getStatus() == 0) {
            applicationEventPublisher.publishEvent(new StartQueueConsumerEvent(this, Collections.singletonList(taskPoolUpdateStatusDto.getId()), null));
        }
        return count;
    }

    @Override
    public TaskPoolEntity getTaskPool(Long taskPoolId) {
        return taskPoolRepository.getById(taskPoolId);
    }

    @Override
    public PageList<TaskPoolPlatformRelEntity> getBindingPlatformList(PlatformListBindingDto param, Page page) {
        return taskPoolPlatformRelRepository.selectListByCondition(param.getTaskPoolId(), param.getPlatformCode(),
                param.getPlatformName(), param.getLabel(), page);
    }

    @Override
    public PageList<PlatformListResultDO> getUnboundPlatformList(PlatformListBindingDto param, Page page) {
        //查询全部私有化平台信息
        List<PlatformListResultDO> platformList = operationService.getPlatformList(param.getPlatformName(),
                StringUtils.isNotBlank(param.getPlatformCode()) ? Collections.singletonList(param.getPlatformCode()) : null, param.getLabel());
        if (CollectionUtils.isEmpty(platformList)) {
            return new PageList<>();
        }
        //查询已绑定的平台信息
        List<String> platformCodeList = taskPoolPlatformRelRepository.getAllPlatformCode();
        platformList = platformList.stream().filter(p -> !platformCodeList.contains(p.getPlatCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(platformList)) {
            return new PageList<>();
        }
        return CommonUtil.getPage(platformList, page);
    }

    @Override
    public void bindPlatform(PlatformBindDto param) {
        List<PlatformListResultDO> platformList;
        if (CollectionUtils.isNotEmpty(param.getPlatformCodes())) {
            platformList = operationService.getPlatformList(null, param.getPlatformCodes(), null);
        } else {
            platformList = operationService.getPlatformList(param.getFilterParam().getPlatformName(),
                    StringUtils.isNotBlank(param.getFilterParam().getPlatformCode())
                            ? Collections.singletonList(param.getFilterParam().getPlatformCode()) : null, param.getFilterParam().getLabel());
        }
        if (CollectionUtils.isEmpty(platformList)) {
            throw new MedicalBusinessException("未查询到对应平台配置");
        }
        //查询任务池已绑定的平台信息
        List<String> platformCodeList = taskPoolPlatformRelRepository.getAllPlatformCode();
        platformList = platformList.stream().filter(p -> !platformCodeList.contains(p.getPlatCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(platformList)) {
            return;
        }
        List<TaskPoolPlatformRelEntity> saveList = new ArrayList<>();
        for (PlatformListResultDO platResultDO : platformList) {
            saveList.add(TaskPoolPlatformRelEntity.builder().id(uidService.getUID()).taskPoolId(param.getTaskPoolId())
                    .platformCode(platResultDO.getPlatCode()).platformName(platResultDO.getPlatName()).type(platResultDO.getType())
                    .label(platResultDO.getLabel()).createdBy(param.getOperator()).createdTime(new Date()).build());
        }
        taskPoolPlatformRelRepository.batchInsert(saveList);
    }

    @Override
    public void unboundPlatform(PlatformBindDto param) {
        if (CollectionUtils.isNotEmpty(param.getPlatformCodes())) {
            taskPoolPlatformRelRepository.unboundPlatform(param.getTaskPoolId(), param.getPlatformCodes());
        } else {
            taskPoolPlatformRelRepository.unboundPlatform(param.getTaskPoolId(), param.getFilterParam().getPlatformCode(),
                    param.getFilterParam().getPlatformName(), param.getFilterParam().getLabel());
        }
    }

}
