package com.iflytek.aicc.task.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.aicc.task.infrastructure.repository.dataobject.TaskPoolGatherDO;
import com.iflytek.aicc.task.infrastructure.repository.dataobject.TaskPoolDO;
import com.iflytek.aicc.task.infrastructure.repository.dataobject.TaskPoolListDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 任务池表mapper
 *
 * <AUTHOR>
 * @date 2023-08-09
 **/
@Mapper
public interface TaskPoolMapper extends BaseMapper<TaskPoolDO> {


    /**
     * 把任务池默认修改成非默认
     */

    void updateToNoDefaultPool(TaskPoolDO taskPoolDO);

    /**
     * 批量保存任务池固化表
     *
     * @param gatherDOList 任务池固化数据
     */
    void batchInsertGather(List<TaskPoolGatherDO> gatherDOList);

    /**
     * 查询任务池列表
     *
     * @param name 名称
     */
    List<TaskPoolListDO> selectTaskPoolList(String name);
}
