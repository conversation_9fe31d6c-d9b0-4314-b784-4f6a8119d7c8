package com.iflytek.aicc.task.application.impl;

import com.iflytek.aicc.task.application.SipRecordService;
import com.iflytek.aicc.task.application.config.ElasticSearchConfig;
import com.iflytek.aicc.task.application.dto.SipRecordSearchDto;
import com.iflytek.aicc.task.domain.entity.CallTaskEntity;
import com.iflytek.aicc.task.domain.entity.SipRecordEntity;
import com.iflytek.aicc.task.domain.repository.CallTaskRepository;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.outbound.utils.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 类说明 记录轨迹查询接口
 *
 * <AUTHOR>
 * @date 2022/9/1
 */
@Service
@Slf4j
public class SipRecordServiceImpl implements SipRecordService {

    private final RestHighLevelClient restHighLevelClient;
    private final CallTaskRepository callTaskRepository;

    private final ElasticSearchConfig elasticSearchConfig;

    @Autowired
    public SipRecordServiceImpl(RestHighLevelClient restHighLevelClient, CallTaskRepository callTaskRepository,
                                ElasticSearchConfig elasticSearchConfig) {
        this.restHighLevelClient = restHighLevelClient;
        this.callTaskRepository = callTaskRepository;
        this.elasticSearchConfig = elasticSearchConfig;
    }

    @Override
    public List<SipRecordEntity> callSipRecord(Long id) {
        CallTaskEntity callTask = callTaskRepository.getCallTaskById(id);
        if (callTask == null) {
            throw new MedicalBusinessException("任务记录为空");
        }
        String callId = id.toString();
        String date = new DateTime(callTask.getTaskTime()).toString("yyyyMMdd");
        String sipLogIndex = elasticSearchConfig.getIndex() + date;

        List<SipRecordSearchDto> searchSipInfo = searchSipInfo(callId, sipLogIndex);
        if (CollectionUtils.isEmpty(searchSipInfo)) {
            return Collections.emptyList();
        }
        Pattern ipRex = Pattern.compile("\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}");
        Pattern portRes = Pattern.compile(":\\d+");

        List<SipRecordEntity> sipRecordList = new ArrayList<>();
        for (SipRecordSearchDto sipRecord : searchSipInfo) {
            SipRecordEntity.SipRecordEntityBuilder builder = SipRecordEntity.builder();
            if (StringUtils.isBlank(sipRecord.getSip_info())) {
                continue;
            }
            builder.callId(callId).sipInfo(sipRecord.getSip_info());
            String[] split = sipRecord.getSip_info().split("\n------------------------------------------------------------------------\n");
            String typeLine = split[0];
            if (typeLine.startsWith("send")) {
                builder.type("send");
            } else if (typeLine.startsWith("recv")) {
                builder.type("recv");
            } else {
                builder.type("unknown");
            }
            String timestampStr = typeLine.substring(typeLine.indexOf("at") + 3, typeLine.length() - 1);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm:ss.SSSSSS");
            LocalTime time = LocalTime.parse(timestampStr, formatter);
            builder.time(time).timestamp(date + "-" + timestampStr);
            //header处理
            String headLine = split[1];
            String[] headlineArr = headLine.split("\n");
            builder.cSeq(headlineArr[0]);
            String fromIpLine = Arrays.stream(headlineArr).filter(s -> s.startsWith("Via:")).findFirst().orElse(null);
            if (StringUtils.isNotBlank(fromIpLine)) {
                Matcher ipMatch = ipRex.matcher(fromIpLine);
                while (ipMatch.find()) {
                    builder.fromIp(ipMatch.group(0));
                }
                Matcher portMatch = portRes.matcher(fromIpLine);
                while (portMatch.find()) {
                    builder.fromPort(portMatch.group(0));
                }
            }
            String toIpLine = Arrays.stream(headlineArr).filter(s -> s.startsWith("To:")).findFirst().orElse(null);
            if (StringUtils.isNotBlank(toIpLine)) {
                Matcher ipMatch = ipRex.matcher(toIpLine);
                while (ipMatch.find()) {
                    builder.toIp(ipMatch.group(0));
                }
                Matcher portMatch = portRes.matcher(toIpLine);
                while (portMatch.find()) {
                    builder.toPort(portMatch.group(0));
                }
            }
            String[] mediaArr = headLine.split("\n\n");
            if (mediaArr.length > 1) {
                builder.mediaInformation(mediaArr[1]);
            }
            sipRecordList.add(builder.build());
        }
        sipRecordList.sort(Comparator.comparing(SipRecordEntity::getTime));
        return sipRecordList;
    }

    private List<SipRecordSearchDto> searchSipInfo(String recordId, String sipLogIndex) {
        SearchRequest searchRequest = new SearchRequest();
        searchRequest.indices(sipLogIndex);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        QueryBuilder queryBuilder = QueryBuilders.termQuery("sip_header_call_id", recordId);
        searchSourceBuilder.query(queryBuilder);
        searchRequest.source(searchSourceBuilder);
        List<SipRecordSearchDto> list = new ArrayList<>();
        try {
            SearchResponse search = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits hits = search.getHits();
            SearchHit[] searchHits = hits.getHits();
            for (SearchHit searchHit : searchHits) {
                SipRecordSearchDto sipRecordEntity = JacksonUtils.readValue(searchHit.getSourceAsString(), SipRecordSearchDto.class);
                list.add(sipRecordEntity);
            }
        } catch (IOException e) {
            log.error("elasticSearch调用查询异常", e);
        }
        return list;
    }
}