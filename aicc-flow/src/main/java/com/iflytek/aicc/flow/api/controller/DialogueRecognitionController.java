package com.iflytek.aicc.flow.api.controller;

import com.iflytek.aicc.flow.infrastructure.rpc.iat.dialogue.dto.DialogueRecognitionRequest;
import com.iflytek.aicc.flow.infrastructure.rpc.iat.dialogue.dto.DialogueRecognitionResult;
import com.iflytek.aicc.flow.infrastructure.rpc.iat.dialogue.service.DialogueRecognitionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * 双声道对话识别控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/iat/dialogue")
@Tag(name = "双声道对话识别", description = "双声道音频对话转文字服务")
@Slf4j
public class DialogueRecognitionController {

    @Autowired
    private DialogueRecognitionService dialogueRecognitionService;

    /**
     * 双声道对话识别
     */
    @PostMapping(value = "/recognize", consumes = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    @Operation(summary = "双声道对话识别", description = "将双声道音频转换为结构化的问答对话文本")
    public CompletableFuture<DialogueRecognitionResult> recognizeDialogue(
            @RequestBody byte[] stereoAudioData,
            @Parameter(description = "采样率") @RequestParam(defaultValue = "16k") String rate,
            @Parameter(description = "音频编码格式") @RequestParam(defaultValue = "raw") String aue,
            @Parameter(description = "返回结果格式") @RequestParam(defaultValue = "json") String rst,
            @Parameter(description = "音频位深") @RequestParam(defaultValue = "16") String bit,
            @Parameter(description = "是否启用降噪") @RequestParam(defaultValue = "true") String denoiseEnable,
            @Parameter(description = "是否获取中间结果") @RequestParam(defaultValue = "wpgs") String dwa,
            @Parameter(description = "PGS模式") @RequestParam(defaultValue = "complex") String pgsMode,
            @Parameter(description = "VAD后断点值(毫秒)") @RequestParam(defaultValue = "3000") String eos,
            @Parameter(description = "热词配置") @RequestParam(required = false) String hotWord,
            @Parameter(description = "最小回应时间间隔(毫秒)") @RequestParam(defaultValue = "200") Long minResponseInterval,
            @Parameter(description = "最大回应时间间隔(毫秒)") @RequestParam(defaultValue = "3000") Long maxResponseInterval,
            @Parameter(description = "最小发言持续时间(毫秒)") @RequestParam(defaultValue = "500") Long minSpeechDuration,
            @Parameter(description = "声道切换容忍时间(毫秒)") @RequestParam(defaultValue = "100") Long channelSwitchTolerance,
            @Parameter(description = "是否启用智能对话分段") @RequestParam(defaultValue = "true") Boolean enableSmartSegmentation) {

        String sessionId = UUID.randomUUID().toString();
        log.info("接收双声道对话识别请求: sessionId={}, 音频大小={}", sessionId, stereoAudioData.length);

        // 构建对齐配置
        DialogueRecognitionRequest.DialogueAlignmentConfig alignmentConfig =
                DialogueRecognitionRequest.DialogueAlignmentConfig.builder()
                        .minResponseInterval(minResponseInterval)
                        .maxResponseInterval(maxResponseInterval)
                        .minSpeechDuration(minSpeechDuration)
                        .channelSwitchTolerance(channelSwitchTolerance)
                        .enableSmartSegmentation(enableSmartSegmentation)
                        .build();

        // 构建识别请求
        DialogueRecognitionRequest request = DialogueRecognitionRequest.builder()
                .sessionId(sessionId)
                .stereoAudioData(stereoAudioData)
                .rate(rate)
                .aue(aue)
                .rst(rst)
                .bit(bit)
                .denoiseEnable(denoiseEnable)
                .dwa(dwa)
                .pgsMode(pgsMode)
                .eos(eos)
                .hotWord(hotWord)
                .alignmentConfig(alignmentConfig)
                .build();

        return dialogueRecognitionService.recognizeDialogue(request);
    }

    /**
     * 获取对话识别状态
     */
    @GetMapping("/status/{sessionId}")
    @Operation(summary = "获取识别状态", description = "查询指定会话的识别状态")
    public CompletableFuture<DialogueRecognitionStatusResponse> getRecognitionStatus(
            @Parameter(description = "会话ID") @PathVariable String sessionId) {

        log.info("查询对话识别状态: sessionId={}", sessionId);

        // 这里可以实现状态查询逻辑，比如从缓存或数据库中查询
        // 目前返回一个简单的响应
        DialogueRecognitionStatusResponse response = DialogueRecognitionStatusResponse.builder()
                .sessionId(sessionId)
                .status("COMPLETED") // 可以是 PROCESSING, COMPLETED, ERROR
                .message("识别已完成")
                .timestamp(System.currentTimeMillis())
                .build();

        return CompletableFuture.completedFuture(response);
    }

    /**
     * 对话识别状态响应
     */
    @Getter
    public static class DialogueRecognitionStatusResponse {
        // Getters
        private final String sessionId;
        private final String status;
        private final String message;
        private final Long timestamp;

        private DialogueRecognitionStatusResponse(Builder builder) {
            this.sessionId = builder.sessionId;
            this.status = builder.status;
            this.message = builder.message;
            this.timestamp = builder.timestamp;
        }

        public static Builder builder() {
            return new Builder();
        }

        public static class Builder {
            private String sessionId;
            private String status;
            private String message;
            private Long timestamp;

            public Builder sessionId(String sessionId) {
                this.sessionId = sessionId;
                return this;
            }

            public Builder status(String status) {
                this.status = status;
                return this;
            }

            public Builder message(String message) {
                this.message = message;
                return this;
            }

            public Builder timestamp(Long timestamp) {
                this.timestamp = timestamp;
                return this;
            }

            public DialogueRecognitionStatusResponse build() {
                return new DialogueRecognitionStatusResponse(this);
            }
        }
    }
}
