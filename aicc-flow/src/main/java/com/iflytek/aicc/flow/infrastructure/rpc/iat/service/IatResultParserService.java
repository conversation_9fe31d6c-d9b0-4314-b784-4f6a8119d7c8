package com.iflytek.aicc.flow.infrastructure.rpc.iat.service;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.iflytek.aicc.flow.infrastructure.rpc.iat.auto.IatOuterClass.IatResult;
import com.iflytek.aicc.flow.infrastructure.rpc.iat.dialogue.dto.DialogueIatResponseVO;
import com.iflytek.aicc.flow.infrastructure.rpc.iat.dialogue.dto.WordTimingInfo;
import com.iflytek.aicc.flow.infrastructure.rpc.iat.dto.IatRecognitionResult;
import com.iflytek.aicc.flow.infrastructure.rpc.iat.dto.IatResponseVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * IAT结果解析服务 - 重构版本
 *
 * 支持三种在线识别模式：
 * 1. 无中间结果模式：直接返回最终识别结果
 * 2. 简单中间结果模式(Simple)：使用 pgs_1 字段标识中间/最终结果
 * 3. 复杂中间结果模式(Complex)：使用 pgs 字段(apd/rpl)标识结果类型，rpl模式包含rg替换范围
 */
@Service
@Slf4j
public class IatResultParserService {

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 解析IAT识别结果的主入口方法
     *
     * @param iatResult IAT原始返回结果
     * @param sessionId 会话ID
     * @param resultFormat 结果格式("plain"或"json")
     * @param includeJsonResult 是否包含原始JSON结果数据
     * @return 解析后的统一响应对象
     */
    public IatResponseVO parseResult(IatResult iatResult, String sessionId, String resultFormat, boolean includeJsonResult) {
        try {
            // 1. 检查错误状态
            if (iatResult.getErrCode() != 0) {
                return buildErrorResponse(sessionId, iatResult.getErrCode(), iatResult.getErrStr());
            }

            // 2. 检查空结果
            String ansStr = iatResult.getAnsStr();
            if (StringUtils.isBlank(ansStr)) {
                return buildEmptyResponse(sessionId, iatResult.getEndFlag());
            }

            // 3. 纯文本格式直接返回
            if ("plain".equals(resultFormat)) {
                return buildPlainTextResponse(ansStr, sessionId, iatResult.getEndFlag());
            }

            // 4. JSON格式解析
            return parseJsonResult(ansStr, sessionId, iatResult.getEndFlag(), includeJsonResult);

        } catch (Exception e) {
            log.error("IAT结果解析失败, sessionId: {}, result: {}", sessionId, iatResult, e);
            return buildErrorResponse(sessionId, -1, "结果解析失败: " + e.getMessage());
        }
    }

    /**
     * 兼容性方法 - 默认包含JSON结果
     */
    public IatResponseVO parseResult(IatResult iatResult, String sessionId, String resultFormat) {
        return parseResult(iatResult, sessionId, resultFormat, true);
    }

    /**
     * 专门为对话识别解析结果，提取词级别时间信息
     *
     * @param iatResult IAT原始返回结果
     * @param sessionId 会话ID
     * @param channelType 声道类型标识
     * @return 包含词级时间信息的解析结果
     */
    public DialogueIatResponseVO parseDialogueResult(IatResult iatResult, String sessionId, String channelType) {
        try {
            // 1. 检查错误状态
            if (iatResult.getErrCode() != 0) {
                return buildDialogueErrorResponse(sessionId, channelType, iatResult.getErrCode(), iatResult.getErrStr());
            }

            // 2. 检查空结果
            String ansStr = iatResult.getAnsStr();
            if (StringUtils.isBlank(ansStr)) {
                return buildDialogueEmptyResponse(sessionId, channelType, iatResult.getEndFlag());
            }

            // 3. 解析JSON格式结果
            return parseDialogueJsonResult(ansStr, sessionId, channelType, iatResult.getEndFlag());

        } catch (Exception e) {
            log.error("对话IAT结果解析失败, sessionId: {}, channel: {}", sessionId, channelType, e);
            return buildDialogueErrorResponse(sessionId, channelType, -1, "结果解析失败: " + e.getMessage());
        }
    }

    /**
     * 解析对话模式的JSON结果，提取词级时间信息
     */
    private DialogueIatResponseVO parseDialogueJsonResult(String ansStr, String sessionId, String channelType, boolean endFlag) {
        try {
            IatRecognitionResult result = objectMapper.readValue(ansStr, IatRecognitionResult.class);

            // 提取识别文本
            String text = extractRecognizedText(result);

            // 提取词级时间信息
            List<WordTimingInfo> wordTimings = extractWordTimings(result);

            // 判断识别模式和结果状态
            RecognitionMode mode = determineRecognitionMode(result);
            ResultStatus status = determineResultStatus(result, endFlag, mode);

            log.debug("对话解析结果 - sessionId: {}, channel: {}, mode: {}, status: {}, text: {}, words: {}",
                    sessionId, channelType, mode, status, text, wordTimings.size());

            return DialogueIatResponseVO.builder()
                    .sessionId(sessionId)
                    .channelType(channelType)
                    .errCode(0)
                    .finished(status.isFinished())
                    .text(text)
                    .timestamp(System.currentTimeMillis())
                    .segmentNumber(result.getSn())
                    .beginTime(Long.valueOf(result.getBg()))
                    .endTime(Long.valueOf(result.getEd()))
                    .energy(result.getEg())
                    .isIntermediate(status.isIntermediate())
                    .wordTimings(wordTimings)
                    .rawResult(result)
                    .build();

        } catch (Exception e) {
            log.error("对话JSON结果解析失败: {}", ansStr, e);
            throw new RuntimeException("对话JSON结果解析失败", e);
        }
    }

    /**
     * 提取词级时间信息
     */
    private List<WordTimingInfo> extractWordTimings(IatRecognitionResult result) {
        List<WordTimingInfo> wordTimings = new ArrayList<>();

        if (result.getWs() != null) {
            for (IatRecognitionResult.WordSegment ws : result.getWs()) {
                if (ws.getCw() != null) {
                    for (IatRecognitionResult.CandidateWord cw : ws.getCw()) {
                        WordTimingInfo wordTiming = WordTimingInfo.builder()
                                .word(cw.getW())
                                .startTime(ws.getBg() != null ? ws.getBg().longValue() : 0L)
                                .endTime(ws.getEd() != null ? ws.getEd().longValue() : 0L)
                                .confidence(cw.getSc() != null ? cw.getSc() : 0.0)
                                .build();
                        wordTimings.add(wordTiming);
                    }
                }
            }
        }

        return wordTimings;
    }

    /**
     * 构建对话错误响应
     */
    private DialogueIatResponseVO buildDialogueErrorResponse(String sessionId, String channelType, int errCode, String errMsg) {
        return DialogueIatResponseVO.builder()
                .sessionId(sessionId)
                .channelType(channelType)
                .errCode(errCode)
                .errMsg(errMsg)
                .finished(true)
                .text("")
                .timestamp(System.currentTimeMillis())
                .wordTimings(new ArrayList<>())
                .build();
    }

    /**
     * 构建对话空响应
     */
    private DialogueIatResponseVO buildDialogueEmptyResponse(String sessionId, String channelType, boolean endFlag) {
        return DialogueIatResponseVO.builder()
                .sessionId(sessionId)
                .channelType(channelType)
                .errCode(0)
                .finished(endFlag)
                .text("")
                .timestamp(System.currentTimeMillis())
                .wordTimings(new ArrayList<>())
                .build();
    }

    /**
     * 解析JSON格式的识别结果
     *
     * 输入示例：
     * 无中间结果: {"sn":1,"bg":0,"ed":0,"eg":0,"ls":true,"ws":[{"bg":0,"cw":[{"sc":0.0,"w":"想"}]}]}
     * Simple模式: {"sn":1,"bg":0,"ed":0,"eg":0,"ls":true,"pgs_1":1,"ws":[{"bg":0,"cw":[{"sc":0.0,"w":"想"}],"ed":0}]}
     * Complex模式APD: {"sn":1,"bg":0,"ed":0,"eg":0,"ls":false,"pgs":"apd","ws":[{"bg":0,"cw":[{"sc":0.0,"w":"你"}]}]}
     * Complex模式RPL: {"sn":1,"bg":0,"ed":0,"eg":0,"ls":false,"pgs":"rpl","rg":[1,2],"ws":[{"bg":0,"cw":[{"sc":0.0,"w":"你好"}]}]}
     *
     * @param ansStr JSON格式的识别结果字符串
     * @param sessionId 会话ID
     * @param endFlag 是否为结束标志
     * @param includeJsonResult 是否包含原始JSON结果数据
     * @return 解析后的响应对象
     */
    private IatResponseVO parseJsonResult(String ansStr, String sessionId, boolean endFlag, boolean includeJsonResult) {
        try {
            IatRecognitionResult result = objectMapper.readValue(ansStr, IatRecognitionResult.class);
            log.debug("解析初始识别结果 - sessionId: {}, includeJson: {}", sessionId, includeJsonResult);

            // 提取识别文本
            String text = extractRecognizedText(result);

            // 判断识别模式和结果状态
            RecognitionMode mode = determineRecognitionMode(result);
            ResultStatus status = determineResultStatus(result, endFlag, mode);

            // 解析复杂模式的替换范围信息
            ComplexModeInfo complexInfo = parseComplexModeInfo(result, mode);

            log.debug("解析结果 - sessionId: {}, mode: {}, status: {}, text: {}",
                    sessionId, mode, status, text);

            return IatResponseVO.builder()
                    .type("online")
                    .sessionId(sessionId)
                    .errCode(0)
                    .finished(status.isFinished())
                    .text(text)
                    .jsonResult(includeJsonResult ? result : null)  // 根据参数决定是否包含原始数据
                    .timestamp(System.currentTimeMillis())
                    .segmentNumber(result.getSn())
                    .beginTime(result.getBg())
                    .endTime(result.getEd())
                    .energy(result.getEg())
                    .isIntermediate(status.isIntermediate())
                    .pgsType(result.getPgs())
                    .pgsRange(complexInfo.getRgValue())
                    .pgsMode((mode.name().toLowerCase()))
                    .pgs1Value(result.getPgs1())
                    .replaceRange(complexInfo.getReplaceRange())
                    .replaceOperation(complexInfo.isReplaceOperation())
                    .build();

        } catch (Exception e) {
            log.error("JSON结果解析失败: {}", ansStr, e);
            throw new RuntimeException("JSON结果解析失败", e);
        }
    }

    /**
     * 从识别结果中提取文本内容
     *
     * 解析过程：
     * 1. 遍历 ws 数组(词段列表)
     * 2. 从每个词段的 cw 数组中取第一个候选词
     * 3. 提取候选词的 w 字段(实际文本)
     * 4. 拼接成完整句子
     *
     * 输入示例: "ws":[{"bg":0,"cw":[{"sc":0.9,"w":"你"},{"sc":0.1,"w":"泥"}]},{"bg":100,"cw":[{"sc":0.8,"w":"好"}]}]
     * 输出结果: "你好"
     *
     * @param result 识别结果对象
     * @return 拼接后的完整文本
     */
    private String extractRecognizedText(IatRecognitionResult result) {
        if (result.getWs() == null || result.getWs().isEmpty()) {
            return "";
        }

        StringBuilder textBuilder = new StringBuilder();
        for (IatRecognitionResult.WordSegment wordSegment : result.getWs()) {
            if (wordSegment.getCw() != null && !wordSegment.getCw().isEmpty()) {
                // 取置信度最高的候选词(第一个)
                IatRecognitionResult.CandidateWord bestCandidate = wordSegment.getCw().get(0);
                if (StringUtils.isNotBlank(bestCandidate.getW())) {
                    textBuilder.append(bestCandidate.getW());
                }
            }
        }

        return textBuilder.toString();
    }

    /**
     * 判断识别模式
     *
     * 判断逻辑：
     * - 存在 pgs_1 字段 → Simple模式 (简单中间结果)
     * - 存在 pgs 字段 → Complex模式 (复杂中间结果)
     * - 都不存在 → NoIntermediate模式 (无中间结果)
     *
     * @param result 识别结果
     * @return 识别模式枚举
     */
    private RecognitionMode determineRecognitionMode(IatRecognitionResult result) {
        if (result.getPgs1() != null) {
            return RecognitionMode.SIMPLE_INTERMEDIATE;
        }
        if (StringUtils.isNotBlank(result.getPgs())) {
            return RecognitionMode.COMPLEX_INTERMEDIATE;
        }
        return RecognitionMode.NO_INTERMEDIATE;
    }

    /**
     * 判断结果状态(中间结果还是最终结果)
     *
     * 不同模式的判断规则：
     *
     * 1. 无中间结果模式：
     *    - ls=true → 最终结果
     *    - ls=false → 中间结果(理论上不应出现)
     *
     * 2. Simple模式：
     *    - pgs_1=1 → 最终结果
     *    - pgs_1=0 → 中间结果
     *
     * 3. Complex模式：
     *    - pgs="apd" → 中间结果开始(追加模式)
     *    - pgs="rpl" → 中间结果替换(替换模式，包含rg替换范围)
     *    - ls=true → 最终结果
     *
     * @param result 识别结果
     * @param endFlag 结束标志
     * @param mode 识别模式
     * @return 结果状态
     */
    private ResultStatus determineResultStatus(IatRecognitionResult result, boolean endFlag, RecognitionMode mode) {
        switch (mode) {
            case NO_INTERMEDIATE:
                // 无中间结果：直接根据ls字段判断
                boolean isFinal = result.getLs() != null && result.getLs();
                return new ResultStatus(isFinal || endFlag, !isFinal && !endFlag);

            case SIMPLE_INTERMEDIATE:
                // Simple模式：pgs_1=1表示最终结果，0表示中间结果
                boolean isSimpleFinal = result.getPgs1() != null && result.getPgs1() == 1;
                return new ResultStatus(isSimpleFinal || endFlag, !isSimpleFinal && !endFlag);

            case COMPLEX_INTERMEDIATE:
                // Complex模式：apd/rpl为中间结果，ls=true为最终结果
                boolean isComplexIntermediate = "apd".equals(result.getPgs()) || "rpl".equals(result.getPgs());
                boolean isComplexFinal = result.getLs() != null && result.getLs();
                return new ResultStatus(isComplexFinal || endFlag, isComplexIntermediate && !isComplexFinal && !endFlag);

            default:
                return new ResultStatus(endFlag, !endFlag);
        }
    }

    /**
     * 解析复杂模式的相关信息
     *
     * Complex模式说明：
     * - pgs="apd": 追加模式，表示中间结果的开始，不包含rg字段
     * - pgs="rpl": 替换模式，表示此次中间结果需要替换之前的某些段，包含rg字段
     *
     * rg字段示例：
     * - [1,1]: 替换第1段
     * - [1,3]: 替换第1段到第3段
     * - [2,2]: 替换第2段
     *
     * 使用场景：
     * 1. 用户说"我想去北京"
     * 2. 第一次返回: pgs="apd", text="我想"
     * 3. 第二次返回: pgs="rpl", rg=[1,1], text="我想去" (替换第1段)
     * 4. 第三次返回: pgs="rpl", rg=[1,2], text="我想去北京" (替换第1-2段)
     *
     * @param result 识别结果
     * @param mode 识别模式
     * @return 复杂模式信息
     */
    private ComplexModeInfo parseComplexModeInfo(IatRecognitionResult result, RecognitionMode mode) {
        if (mode != RecognitionMode.COMPLEX_INTERMEDIATE) {
            return new ComplexModeInfo(null, null, false);
        }

        String pgsType = result.getPgs();
        List<Integer> rgValue = result.getRg();
        boolean isReplaceOperation = "rpl".equals(pgsType);

        // 构建替换范围描述
        String replaceRange = null;
        if (isReplaceOperation && rgValue != null && rgValue.size() >= 2) {
            if (rgValue.get(0).equals(rgValue.get(1))) {
                replaceRange = String.format("替换第%d段", rgValue.get(0));
            } else {
                replaceRange = String.format("替换第%d-%d段", rgValue.get(0), rgValue.get(1));
            }
        }

        return new ComplexModeInfo(rgValue, replaceRange, isReplaceOperation);
    }

    /**
     * 构建纯文本格式响应
     *
     * 输入示例: ansStr="你好世界", sessionId="session123", endFlag=true
     * 输出: IatResponseVO{type="online", text="你好世界", finished=true, ...}
     */
    private IatResponseVO buildPlainTextResponse(String ansStr, String sessionId, boolean endFlag) {
        return IatResponseVO.builder()
                .type("online")
                .sessionId(sessionId)
                .errCode(0)
                .finished(endFlag)
                .text(ansStr.trim())
                .timestamp(System.currentTimeMillis())
                .isIntermediate(!endFlag)
//                .recognitionMode("plain")
                .build();
    }

    /**
     * 构建错误响应
     */
    private IatResponseVO buildErrorResponse(String sessionId, int errCode, String errMsg) {
        return IatResponseVO.builder()
                .sessionId(sessionId)
                .errCode(errCode)
                .errMsg(errMsg)
                .finished(true)
                .timestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 构建空响应
     */
    private IatResponseVO buildEmptyResponse(String sessionId, boolean endFlag) {
        return IatResponseVO.builder()
                .sessionId(sessionId)
                .errCode(0)
                .finished(endFlag)
                .text("")
                .timestamp(System.currentTimeMillis())
                .isIntermediate(!endFlag)
                .build();
    }

    /**
     * 识别模式枚举
     */
    private enum RecognitionMode {
        NO_INTERMEDIATE,     // 无中间结果
        SIMPLE_INTERMEDIATE, // 简单中间结果(pgs_1)
        COMPLEX_INTERMEDIATE // 复杂中间结果(pgs)
    }

    /**
     * 结果状态内部类
     */
    private static class ResultStatus {
        private final boolean finished;
        private final boolean intermediate;

        ResultStatus(boolean finished, boolean intermediate) {
            this.finished = finished;
            this.intermediate = intermediate;
        }

        public boolean isFinished() {
            return finished;
        }
        public boolean isIntermediate() {
            return intermediate;
        }

        @Override
        public String toString() {
            return String.format("ResultStatus{finished=%s, intermediate=%s}", finished, intermediate);
        }
    }

    /**
     * 复杂模式信息内部类
     * 用于封装Complex模式下的rg字段和替换操作信息
     */
    private static class ComplexModeInfo {
        private final List<Integer> rgValue;        // rg数组原始值，如[1,2]
        private final String replaceRange;     // 替换范围描述，如"替换第1-2段"
        private final boolean replaceOperation; // 是否为替换操作(pgs="rpl")

        ComplexModeInfo(List<Integer> rgValue, String replaceRange, boolean replaceOperation) {
            this.rgValue = rgValue;
            this.replaceRange = replaceRange;
            this.replaceOperation = replaceOperation;
        }

        public List<Integer> getRgValue() {
            return rgValue;
        }
        public String getReplaceRange() {
            return replaceRange;
        }
        public boolean isReplaceOperation() {
            return replaceOperation;
        }

        @Override
        public String toString() {
            return String.format("ComplexModeInfo{rgValue=%s, replaceRange='%s', replaceOperation=%s}",
                    rgValue, replaceRange, replaceOperation);
        }
    }


    /**
     * 从词段中提取单词文本
     */
    private String extractWordText(IatRecognitionResult.WordSegment wordSegment) {
        if (wordSegment.getCw() != null && !wordSegment.getCw().isEmpty()) {
            return wordSegment.getCw().get(0).getW();
        }
        return "";
    }
}
