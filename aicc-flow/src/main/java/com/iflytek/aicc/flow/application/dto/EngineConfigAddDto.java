package com.iflytek.aicc.flow.application.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2023/9/12 10:19
 * @Description
 */
@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class EngineConfigAddDto {
    /**
     * 登录用户
     */
    private String loginUserId;

    private String engineName;

    private String serviceName;

    private int deleted;
    private int type;
    private String engineIntro;
    private Integer defaultEngine;
}
