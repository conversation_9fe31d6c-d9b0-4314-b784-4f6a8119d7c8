package com.iflytek.aicc.flow.infrastructure.rpc.iat.dialogue.service;

import com.iflytek.aicc.flow.infrastructure.rpc.iat.IatServiceFacade;
import com.iflytek.aicc.flow.infrastructure.rpc.iat.auto.IatOuterClass.IatResult;
import com.iflytek.aicc.flow.infrastructure.rpc.iat.dialogue.dto.DialogueRecognitionRequest;
import com.iflytek.aicc.flow.infrastructure.rpc.iat.dialogue.dto.DialogueRecognitionResult;
import com.iflytek.aicc.flow.infrastructure.rpc.iat.dto.IatResponseVO;
import com.iflytek.aicc.flow.infrastructure.rpc.iat.dto.IatSessionParam;
import com.iflytek.aicc.flow.infrastructure.rpc.iat.service.IatResultParserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 双声道对话识别服务
 * 核心服务类，协调音频分离、语音识别、时间轴对齐等功能
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DialogueRecognitionService {

    @Autowired
    private AudioChannelSeparationService channelSeparationService;

    @Autowired
    private DialogueTimelineAlignmentService timelineAlignmentService;

    @Autowired
    private IatServiceFacade iatServiceFacade;

    @Autowired
    private IatResultParserService iatResultParserService;

    /**
     * 执行双声道对话识别
     *
     * @param request 识别请求
     * @return 识别结果
     */
    public CompletableFuture<DialogueRecognitionResult> recognizeDialogue(DialogueRecognitionRequest request) {
        CompletableFuture<DialogueRecognitionResult> future = new CompletableFuture<>();

        try {
            log.info("开始双声道对话识别: sessionId={}", request.getSessionId());
            long startTime = System.currentTimeMillis();

            // 1. 音频预处理和声道分离
            AudioChannelSeparationService.SeparatedChannelData separatedData =
                    separateAudioChannels(request);

            // 2. 并行执行双声道语音识别
            CompletableFuture<List<DialogueRecognitionResult.ChannelRecognitionResult>> leftChannelFuture =
                    recognizeChannel(separatedData.getLeftChannelData(),
                                   DialogueRecognitionResult.ChannelType.LEFT, request);

            CompletableFuture<List<DialogueRecognitionResult.ChannelRecognitionResult>> rightChannelFuture =
                    recognizeChannel(separatedData.getRightChannelData(),
                                   DialogueRecognitionResult.ChannelType.RIGHT, request);

            // 3. 等待双声道识别完成并进行时间轴对齐
            CompletableFuture.allOf(leftChannelFuture, rightChannelFuture)
                    .thenApply(v -> {
                        try {
                            List<DialogueRecognitionResult.ChannelRecognitionResult> leftResults = leftChannelFuture.get();
                            List<DialogueRecognitionResult.ChannelRecognitionResult> rightResults = rightChannelFuture.get();

                            // 4. 时间轴对齐和对话分析
                            List<DialogueRecognitionResult.DialogueTurn> dialogueTurns =
                                    timelineAlignmentService.alignToDialogueTurns(
                                            leftResults, rightResults, request.getAlignmentConfig());

                            // 5. 构建最终结果
                            long processingTime = System.currentTimeMillis() - startTime;
                            DialogueRecognitionResult result = buildFinalResult(
                                    request, leftResults, rightResults, dialogueTurns, processingTime);

                            log.info("双声道对话识别完成: sessionId={}, 处理时间={}ms, 对话轮次={}",
                                    request.getSessionId(), processingTime, dialogueTurns.size());

                            future.complete(result);
                            return result;

                        } catch (Exception e) {
                            log.error("对话识别处理失败: sessionId={}", request.getSessionId(), e);
                            future.completeExceptionally(e);
                            throw new RuntimeException(e);
                        }
                    })
                    .exceptionally(throwable -> {
                        log.error("对话识别异步处理失败: sessionId={}", request.getSessionId(), throwable);
                        future.completeExceptionally(throwable);
                        return null;
                    });

        } catch (Exception e) {
            log.error("对话识别启动失败: sessionId={}", request.getSessionId(), e);
            future.completeExceptionally(e);
        }

        return future;
    }

    /**
     * 分离音频声道
     */
    private AudioChannelSeparationService.SeparatedChannelData separateAudioChannels(
            DialogueRecognitionRequest request) {

        int sampleRate = parseSampleRate(request.getRate());
        int bitDepth = Integer.parseInt(request.getBit());

        // 验证音频格式
        if (!channelSeparationService.validateStereoAudio(request.getStereoAudioData(), sampleRate, bitDepth)) {
            throw new IllegalArgumentException("无效的双声道音频格式");
        }

        return channelSeparationService.separateChannels(request.getStereoAudioData(), sampleRate, bitDepth);
    }

    /**
     * 识别单个声道
     */
    private CompletableFuture<List<DialogueRecognitionResult.ChannelRecognitionResult>> recognizeChannel(
            byte[] channelData,
            DialogueRecognitionResult.ChannelType channelType,
            DialogueRecognitionRequest request) {

        CompletableFuture<List<DialogueRecognitionResult.ChannelRecognitionResult>> future = new CompletableFuture<>();

        try {
            String channelSessionId = request.getSessionId() + "_" + channelType.name();
            log.debug("开始识别声道: channelSessionId={}, 数据大小={}", channelSessionId, channelData.length);

            // 构建识别参数
            IatSessionParam sessionParam = IatSessionParam.builder()
                    .sid(channelSessionId)
                    .rate(request.getRate())
                    .aue(request.getAue())
                    .rst(request.getRst())
                    .bit(request.getBit())
                    .denoiseEnable(request.getDenoiseEnable())
                    .dwa(request.getDwa())
                    .pgsMode(request.getPgsMode())
                    .eos(request.getEos())
                    .hotword(request.getHotWord())
                    .build();

            List<DialogueRecognitionResult.ChannelRecognitionResult> channelResults = new ArrayList<>();

            // 执行语音识别
            iatServiceFacade.recognizeAudio(channelData, sessionParam,
                    (IatResult iatResult) -> {
                        try {
                            // 解析识别结果
                            IatResponseVO parsedResult = iatResultParserService.parseResult(
                                    iatResult, channelSessionId, request.getRst(), false);

                            if (parsedResult.getErrCode() == 0 && parsedResult.getText() != null) {
                                DialogueRecognitionResult.ChannelRecognitionResult channelResult =
                                        DialogueRecognitionResult.ChannelRecognitionResult.builder()
                                                .channel(channelType)
                                                .segmentNumber(parsedResult.getSegmentNumber())
                                                .beginTime(parsedResult.getBeginTime())
                                                .endTime(parsedResult.getEndTime())
                                                .text(parsedResult.getText())
                                                .isFinal(parsedResult.getFinished())
                                                .rawJsonData(parsedResult.getJsonResult())
                                                .build();

                                synchronized (channelResults) {
                                    channelResults.add(channelResult);
                                }

                                log.debug("声道识别片段: channel={}, segment={}, text={}",
                                        channelType, parsedResult.getSegmentNumber(), parsedResult.getText());
                            }

                            // 如果识别完成，返回结果
                            if (parsedResult.getFinished()) {
                                log.info("声道识别完成: channel={}, 片段数={}", channelType, channelResults.size());
                                future.complete(new ArrayList<>(channelResults));
                            }

                        } catch (Exception e) {
                            log.error("声道识别结果处理失败: channel={}", channelType, e);
                            future.completeExceptionally(e);
                        }
                    }, future);

        } catch (Exception e) {
            log.error("声道识别启动失败: channel={}", channelType, e);
            future.completeExceptionally(e);
        }

        return future;
    }

    /**
     * 构建最终识别结果
     */
    private DialogueRecognitionResult buildFinalResult(
            DialogueRecognitionRequest request,
            List<DialogueRecognitionResult.ChannelRecognitionResult> leftResults,
            List<DialogueRecognitionResult.ChannelRecognitionResult> rightResults,
            List<DialogueRecognitionResult.DialogueTurn> dialogueTurns,
            long processingTime) {

        // 计算统计信息
        DialogueRecognitionResult.ProcessingStats stats = calculateProcessingStats(
                leftResults, rightResults, dialogueTurns, processingTime);

        // 构建原始数据
        DialogueRecognitionResult.RawRecognitionData rawData = DialogueRecognitionResult.RawRecognitionData.builder()
                .leftChannelResults(leftResults)
                .rightChannelResults(rightResults)
                .build();

        return DialogueRecognitionResult.builder()
                .sessionId(request.getSessionId())
                .errCode(0)
                .errMsg(null)
                .finished(true)
                .dialogueTurns(dialogueTurns)
                .rawData(rawData)
                .stats(stats)
                .build();
    }

    /**
     * 计算处理统计信息
     */
    private DialogueRecognitionResult.ProcessingStats calculateProcessingStats(
            List<DialogueRecognitionResult.ChannelRecognitionResult> leftResults,
            List<DialogueRecognitionResult.ChannelRecognitionResult> rightResults,
            List<DialogueRecognitionResult.DialogueTurn> dialogueTurns,
            long processingTime) {

        // 计算音频总时长
        long maxEndTime = 0;
        for (DialogueRecognitionResult.ChannelRecognitionResult result : leftResults) {
            maxEndTime = Math.max(maxEndTime, result.getEndTime());
        }
        for (DialogueRecognitionResult.ChannelRecognitionResult result : rightResults) {
            maxEndTime = Math.max(maxEndTime, result.getEndTime());
        }

        // 计算平均回应时间
        double averageResponseTime = calculateAverageResponseTime(dialogueTurns);

        return DialogueRecognitionResult.ProcessingStats.builder()
                .totalProcessingTime(processingTime)
                .totalAudioDuration(maxEndTime)
                .leftChannelSegments(leftResults.size())
                .rightChannelSegments(rightResults.size())
                .totalTurns(dialogueTurns.size())
                .averageResponseTime(averageResponseTime)
                .build();
    }

    /**
     * 计算平均回应时间
     */
    private double calculateAverageResponseTime(List<DialogueRecognitionResult.DialogueTurn> turns) {
        if (turns.size() < 2) return 0.0;

        long totalResponseTime = 0;
        int responseCount = 0;

        for (int i = 1; i < turns.size(); i++) {
            DialogueRecognitionResult.DialogueTurn current = turns.get(i);
            DialogueRecognitionResult.DialogueTurn previous = turns.get(i - 1);

            // 只计算不同说话人之间的回应时间
            if (!current.getSpeakerId().equals(previous.getSpeakerId())) {
                long responseTime = current.getStartTime() - previous.getEndTime();
                if (responseTime > 0) {
                    totalResponseTime += responseTime;
                    responseCount++;
                }
            }
        }

        return responseCount > 0 ? (double) totalResponseTime / responseCount : 0.0;
    }

    /**
     * 解析采样率
     */
    private int parseSampleRate(String rate) {
        switch (rate.toLowerCase()) {
            case "8k": return 8000;
            case "22k": return 22050;
            case "44k": return 44100;
            case "48k": return 48000;
            default: return 16000;
        }
    }
}
