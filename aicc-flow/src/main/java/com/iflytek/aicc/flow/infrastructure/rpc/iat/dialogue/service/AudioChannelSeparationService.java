package com.iflytek.aicc.flow.infrastructure.rpc.iat.dialogue.service;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.sound.sampled.AudioFormat;

/**
 * 音频声道分离服务
 * 负责将双声道音频分离为两个独立的单声道音频
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AudioChannelSeparationService {

    /**
     * 分离双声道音频
     *
     * @param stereoAudioData 双声道音频数据
     * @param sampleRate 采样率
     * @param bitDepth 位深度
     * @return 分离后的声道数据
     */
    public SeparatedChannelData separateChannels(byte[] stereoAudioData, int sampleRate, int bitDepth) {
        try {
            log.info("开始分离双声道音频: 数据大小={}, 采样率={}, 位深={}",
                    stereoAudioData.length, sampleRate, bitDepth);

            // 创建音频格式
            AudioFormat stereoFormat = new AudioFormat(
                    AudioFormat.Encoding.PCM_SIGNED,
                    sampleRate,
                    bitDepth,
                    2, // 双声道
                    (bitDepth / 8) * 2, // 帧大小
                    sampleRate,
                    false // little-endian
            );

            // 创建单声道格式
            AudioFormat monoFormat = new AudioFormat(
                    AudioFormat.Encoding.PCM_SIGNED,
                    sampleRate,
                    bitDepth,
                    1, // 单声道
                    bitDepth / 8, // 帧大小
                    sampleRate,
                    false // little-endian
            );

            // 分离声道
            byte[] leftChannelData = extractChannel(stereoAudioData, 0, bitDepth);
            byte[] rightChannelData = extractChannel(stereoAudioData, 1, bitDepth);

            log.info("声道分离完成: 左声道大小={}, 右声道大小={}",
                    leftChannelData.length, rightChannelData.length);

            return SeparatedChannelData.builder()
                    .leftChannelData(leftChannelData)
                    .rightChannelData(rightChannelData)
                    .monoFormat(monoFormat)
                    .originalStereoFormat(stereoFormat)
                    .sampleRate(sampleRate)
                    .bitDepth(bitDepth)
                    .build();

        } catch (Exception e) {
            log.error("声道分离失败", e);
            throw new RuntimeException("声道分离失败: " + e.getMessage(), e);
        }
    }

    /**
     * 提取指定声道的音频数据
     *
     * @param stereoData 双声道数据
     * @param channelIndex 声道索引 (0=左声道, 1=右声道)
     * @param bitDepth 位深度
     * @return 单声道数据
     */
    private byte[] extractChannel(byte[] stereoData, int channelIndex, int bitDepth) {
        int bytesPerSample = bitDepth / 8;
        int frameSize = bytesPerSample * 2; // 双声道帧大小
        int numFrames = stereoData.length / frameSize;

        byte[] channelData = new byte[numFrames * bytesPerSample];

        for (int frame = 0; frame < numFrames; frame++) {
            int stereoOffset = frame * frameSize + channelIndex * bytesPerSample;
            int monoOffset = frame * bytesPerSample;

            // 复制样本数据
            System.arraycopy(stereoData, stereoOffset, channelData, monoOffset, bytesPerSample);
        }

        return channelData;
    }

    /**
     * 验证音频格式是否为双声道
     *
     * @param audioData 音频数据
     * @param expectedSampleRate 期望采样率
     * @param expectedBitDepth 期望位深度
     * @return 是否为有效的双声道音频
     */
    public boolean validateStereoAudio(byte[] audioData, int expectedSampleRate, int expectedBitDepth) {
        try {
            int expectedFrameSize = (expectedBitDepth / 8) * 2; // 双声道
            int expectedFrames = audioData.length / expectedFrameSize;

            // 检查数据长度是否符合双声道格式
            boolean validLength = (audioData.length % expectedFrameSize) == 0;
            boolean hasMinimumData = expectedFrames > 0;

            log.debug("音频格式验证: 数据长度={}, 帧大小={}, 帧数={}, 长度有效={}, 有最小数据={}",
                    audioData.length, expectedFrameSize, expectedFrames, validLength, hasMinimumData);

            return validLength && hasMinimumData;

        } catch (Exception e) {
            log.error("音频格式验证失败", e);
            return false;
        }
    }

    /**
     * 分离后的声道数据
     */
    @Getter
    public static class SeparatedChannelData {
        // Getters
        private final byte[] leftChannelData;
        private final byte[] rightChannelData;
        private final AudioFormat monoFormat;
        private final AudioFormat originalStereoFormat;
        private final int sampleRate;
        private final int bitDepth;

        private SeparatedChannelData(Builder builder) {
            this.leftChannelData = builder.leftChannelData;
            this.rightChannelData = builder.rightChannelData;
            this.monoFormat = builder.monoFormat;
            this.originalStereoFormat = builder.originalStereoFormat;
            this.sampleRate = builder.sampleRate;
            this.bitDepth = builder.bitDepth;
        }

        public static Builder builder() {
            return new Builder();
        }

        public static class Builder {
            private byte[] leftChannelData;
            private byte[] rightChannelData;
            private AudioFormat monoFormat;
            private AudioFormat originalStereoFormat;
            private int sampleRate;
            private int bitDepth;

            public Builder leftChannelData(byte[] leftChannelData) {
                this.leftChannelData = leftChannelData;
                return this;
            }

            public Builder rightChannelData(byte[] rightChannelData) {
                this.rightChannelData = rightChannelData;
                return this;
            }

            public Builder monoFormat(AudioFormat monoFormat) {
                this.monoFormat = monoFormat;
                return this;
            }

            public Builder originalStereoFormat(AudioFormat originalStereoFormat) {
                this.originalStereoFormat = originalStereoFormat;
                return this;
            }

            public Builder sampleRate(int sampleRate) {
                this.sampleRate = sampleRate;
                return this;
            }

            public Builder bitDepth(int bitDepth) {
                this.bitDepth = bitDepth;
                return this;
            }

            public SeparatedChannelData build() {
                return new SeparatedChannelData(this);
            }
        }
    }
}
