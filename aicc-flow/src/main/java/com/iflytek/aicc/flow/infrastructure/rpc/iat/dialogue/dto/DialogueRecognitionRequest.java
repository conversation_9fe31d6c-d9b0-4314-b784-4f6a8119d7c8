package com.iflytek.aicc.flow.infrastructure.rpc.iat.dialogue.dto;

import lombok.Builder;
import lombok.Data;

/**
 * 双声道对话识别请求参数
 *
 * <AUTHOR>
 */
@Data
@Builder
public class DialogueRecognitionRequest {

    /** 会话ID */
    private String sessionId;

    /** 双声道音频数据 */
    private byte[] stereoAudioData;

    /** 音频采样率 */
    @Builder.Default
    private String rate = "16k";

    /** 音频编码格式 */
    @Builder.Default
    private String aue = "raw";

    /** 返回结果格式 */
    @Builder.Default
    private String rst = "json";

    /** 音频位深 */
    @Builder.Default
    private String bit = "16";

    /** 是否启用降噪 */
    @Builder.Default
    private String denoiseEnable = "true";

    /** 是否获取中间结果 */
    @Builder.Default
    private String dwa = "wpgs";

    /** PGS模式 */
    @Builder.Default
    private String pgsMode = "complex";

    /** VAD后断点值(毫秒) */
    @Builder.Default
    private String eos = "3000";

    /** 热词配置 */
    private String hotWord;

    /** 问答对齐参数 */
    private DialogueAlignmentConfig alignmentConfig;

    @Data
    @Builder
    public static class DialogueAlignmentConfig {
        /** 最小回应时间间隔(毫秒) */
        @Builder.Default
        private long minResponseInterval = 200;

        /** 最大回应时间间隔(毫秒) */
        @Builder.Default
        private long maxResponseInterval = 3000;

        /** 最小发言持续时间(毫秒) */
        @Builder.Default
        private long minSpeechDuration = 500;

        /** 声道切换容忍时间(毫秒) */
        @Builder.Default
        private long channelSwitchTolerance = 100;

        /** 是否启用智能对话分段 */
        @Builder.Default
        private boolean enableSmartSegmentation = true;
    }
}
