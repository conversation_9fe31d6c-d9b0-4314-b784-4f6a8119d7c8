package com.iflytek.aicc.flow.infrastructure.rpc.iat.dialogue.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 双声道对话识别结果
 *
 * <AUTHOR>
 */
@Data
@Builder
public class DialogueRecognitionResult {

    /** 会话ID */
    private String sessionId;

    /** 错误码 */
    private Integer errCode;

    /** 错误信息 */
    private String errMsg;

    /** 是否识别完成 */
    private Boolean finished;

    /** 对话轮次列表 */
    private List<DialogueTurn> dialogueTurns;

    /** 原始识别结果 */
    private RawRecognitionData rawData;

    /** 处理统计信息 */
    private ProcessingStats stats;

    @Data
    @Builder
    public static class DialogueTurn {
        /** 轮次序号 */
        private Integer turnNumber;

        /** 说话人标识 (SPEAKER_A/SPEAKER_B) */
        private String speakerId;

        /** 声道标识 (LEFT/RIGHT) */
        private ChannelType channel;

        /** 识别文本 */
        private String text;

        /** 开始时间(毫秒) */
        private Long startTime;

        /** 结束时间(毫秒) */
        private Long endTime;

        /** 持续时间(毫秒) */
        private Long duration;

        /** 置信度 */
        private Double confidence;

        /** 对话类型 */
        private TurnType turnType;

        /** 词级别时间信息 */
        private List<WordTiming> wordTimings;
    }

    @Data
    @Builder
    public static class WordTiming {
        /** 词内容 */
        private String word;

        /** 开始时间(毫秒) */
        private Long startTime;

        /** 结束时间(毫秒) */
        private Long endTime;

        /** 置信度 */
        private Double confidence;
    }

    @Data
    @Builder
    public static class RawRecognitionData {
        /** 左声道识别结果 */
        private List<ChannelRecognitionResult> leftChannelResults;

        /** 右声道识别结果 */
        private List<ChannelRecognitionResult> rightChannelResults;
    }

    @Data
    @Builder
    public static class ChannelRecognitionResult {
        /** 声道类型 */
        private ChannelType channel;

        /** 段序号 */
        private Integer segmentNumber;

        /** 开始时间 */
        private Long beginTime;

        /** 结束时间 */
        private Long endTime;

        /** 识别文本 */
        private String text;

        /** 是否为最终结果 */
        private Boolean isFinal;

        /** 原始JSON数据 */
        private Object rawJsonData;
    }

    @Data
    @Builder
    public static class ProcessingStats {
        /** 总处理时间(毫秒) */
        private Long totalProcessingTime;

        /** 音频总时长(毫秒) */
        private Long totalAudioDuration;

        /** 左声道识别段数 */
        private Integer leftChannelSegments;

        /** 右声道识别段数 */
        private Integer rightChannelSegments;

        /** 对话轮次总数 */
        private Integer totalTurns;

        /** 平均回应时间(毫秒) */
        private Double averageResponseTime;
    }

    public enum ChannelType {
        LEFT, RIGHT
    }

    public enum TurnType {
        QUESTION,    // 问题
        ANSWER,      // 回答
        STATEMENT,   // 陈述
        INTERRUPTION // 插话
    }
}
