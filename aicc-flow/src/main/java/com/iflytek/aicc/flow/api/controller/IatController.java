package com.iflytek.aicc.flow.api.controller;

import com.iflytek.aicc.flow.infrastructure.rpc.iat.IatServiceFacade;
import com.iflytek.aicc.flow.infrastructure.rpc.iat.auto.IatOuterClass.IatResult;
import com.iflytek.aicc.flow.infrastructure.rpc.iat.dto.IatResponseVO;
import com.iflytek.aicc.flow.infrastructure.rpc.iat.dto.IatSessionParam;
import com.iflytek.aicc.flow.infrastructure.rpc.iat.service.IatResultParserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * IAT语音识别REST控制器
 */
@RestController
@RequestMapping("/v1/pt/iat")
@Slf4j
public class IatController {
    @Autowired
    private IatServiceFacade iatServiceFacade;
    @Autowired
    private IatResultParserService resultParserService;

    /**
     ** 一次性语音识别
     */
    @PostMapping("/recognize")
    public CompletableFuture<IatResponseVO> recognize(
            @RequestBody byte[] audioData,
            @RequestParam(defaultValue = "16k") String rate,
            @RequestParam(defaultValue = "raw") String aue,
            @RequestParam(defaultValue = "json") String rst) {

        CompletableFuture<IatResponseVO> future = new CompletableFuture<>();
        String sessionId = UUID.randomUUID().toString();

        IatSessionParam sessionParam = IatSessionParam.builder()
                .sid(sessionId)
                .rate(rate)
                .aue(aue)
                .rst(rst)
                .build();

        iatServiceFacade.recognizeAudio(audioData, sessionParam, result -> {
            try {
                IatResponseVO response = resultParserService.parseResult(result, sessionId, rst, false);
                future.complete(response);
            } catch (Exception e) {
                future.completeExceptionally(e);
            }
        }, future);

        return future;
    }

    /**
     ** SSE流式识别
     */
    @PostMapping(value = "/recognize-stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter recognizeStream(
            @RequestBody byte[] audioData,
            @RequestParam(defaultValue = "16k") String rate,
            @RequestParam(defaultValue = "raw") String aue,
            @RequestParam(defaultValue = "json") String rst,
            @RequestParam(defaultValue = "wpgs") String dwa,
            @RequestParam(defaultValue = "complex") String pgsMode,
            @RequestParam(defaultValue = "8192") int chunkSize) {

        log.info("SSE流式识别开始: audioSize={}, pgsMode={}, chunkSize={}",
                audioData.length, pgsMode, chunkSize);

        SseEmitter emitter = new SseEmitter(60000L); // 增加到60秒
        String sessionId = UUID.randomUUID().toString();

        IatSessionParam sessionParam = IatSessionParam.builder()
                .sid(sessionId)
                .rate(rate)
                .aue(aue)
                .rst(rst)
                .dwa(dwa)
                .pgsMode(pgsMode)
                .build();

        // 异步处理，避免阻塞请求线程
        CompletableFuture.runAsync(() -> {
            try {
                iatServiceFacade.recognizeAudioWithChunks(
                        audioData,
                        sessionParam,
                        chunkSize,
                        result -> handleSseResult(emitter, sessionId, result, rst)
                );
            } catch (Exception e) {
                log.error("SSE流式识别失败: sessionId={}", sessionId, e);
                try {
                    emitter.completeWithError(e);
                } catch (Exception ex) {
                    log.warn("SSE错误通知失败: sessionId={}", sessionId, ex);
                }
            }
        });

        // 设置超时和错误处理
        emitter.onTimeout(() -> {
            log.warn("SSE连接超时: sessionId={}", sessionId);
            emitter.complete();
        });

        emitter.onError(throwable -> {
            log.error("SSE连接错误: sessionId={}", sessionId, throwable);
        });

        return emitter;
    }

    /**
     * 处理SSE结果回调
     */
    private void handleSseResult(SseEmitter emitter, String sessionId, IatResult result, String rst) {
        try {
            // 解析识别结果，不包含原始JSON数据以减少传输量
            IatResponseVO response = resultParserService.parseResult(result, sessionId, rst, false);

            // 通过SSE发送结果
            emitter.send(SseEmitter.event()
                    .name("result")
                    .data(response));

            log.debug("SSE发送结果: sessionId={}, pgsType={}, finished={}, text=[{}]",
                    sessionId, response.getPgsType(), response.getFinished(),
                    response.getText());

            // 检查是否结束
            if (response.getFinished()) {
                log.info("SSE流式识别完成: sessionId={}", sessionId);
                emitter.complete();
            }

        } catch (Exception e) {
            log.error("SSE发送结果失败: sessionId={}", sessionId, e);
            try {
                emitter.completeWithError(e);
            } catch (Exception ex) {
                log.warn("SSE错误完成失败: sessionId={}", sessionId, ex);
            }
        }
    }
}
