package com.iflytek.aicc.flow.infrastructure.rpc.iat.dialogue.service;

import com.iflytek.aicc.flow.infrastructure.rpc.iat.dialogue.dto.DialogueRecognitionResult;
import com.iflytek.aicc.flow.infrastructure.rpc.iat.dialogue.dto.DialogueRecognitionRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 对话时间轴对齐服务
 * 负责将双声道识别结果按时间轴对齐为问答对话形式
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DialogueTimelineAlignmentService {

    /**
     * 对齐双声道识别结果为对话轮次
     *
     * @param leftChannelResults 左声道识别结果
     * @param rightChannelResults 右声道识别结果
     * @param alignmentConfig 对齐配置
     * @return 对齐后的对话轮次列表
     */
    public List<DialogueRecognitionResult.DialogueTurn> alignToDialogueTurns(
            List<DialogueRecognitionResult.ChannelRecognitionResult> leftChannelResults,
            List<DialogueRecognitionResult.ChannelRecognitionResult> rightChannelResults,
            DialogueRecognitionRequest.DialogueAlignmentConfig alignmentConfig) {

        log.info("开始对话时间轴对齐: 左声道段数={}, 右声道段数={}", 
                leftChannelResults.size(), rightChannelResults.size());

        // 1. 合并所有识别结果并按时间排序
        List<TimelineSegment> allSegments = mergeAndSortSegments(leftChannelResults, rightChannelResults);

        // 2. 过滤掉过短的片段
        List<TimelineSegment> filteredSegments = filterShortSegments(allSegments, alignmentConfig);

        // 3. 识别对话轮次
        List<DialogueRecognitionResult.DialogueTurn> dialogueTurns = identifyDialogueTurns(filteredSegments, alignmentConfig);

        // 4. 分析对话模式并标记问答类型
        dialogueTurns = analyzeDialoguePattern(dialogueTurns, alignmentConfig);

        log.info("对话对齐完成: 生成对话轮次={}", dialogueTurns.size());
        return dialogueTurns;
    }

    /**
     * 合并并排序所有识别片段
     */
    private List<TimelineSegment> mergeAndSortSegments(
            List<DialogueRecognitionResult.ChannelRecognitionResult> leftResults,
            List<DialogueRecognitionResult.ChannelRecognitionResult> rightResults) {

        List<TimelineSegment> allSegments = new ArrayList<>();

        // 添加左声道片段
        for (DialogueRecognitionResult.ChannelRecognitionResult result : leftResults) {
            if (result.getText() != null && !result.getText().trim().isEmpty()) {
                allSegments.add(TimelineSegment.builder()
                        .channel(DialogueRecognitionResult.ChannelType.LEFT)
                        .speakerId("SPEAKER_A")
                        .text(result.getText().trim())
                        .startTime(result.getBeginTime())
                        .endTime(result.getEndTime())
                        .isFinal(result.getIsFinal())
                        .segmentNumber(result.getSegmentNumber())
                        .build());
            }
        }

        // 添加右声道片段
        for (DialogueRecognitionResult.ChannelRecognitionResult result : rightResults) {
            if (result.getText() != null && !result.getText().trim().isEmpty()) {
                allSegments.add(TimelineSegment.builder()
                        .channel(DialogueRecognitionResult.ChannelType.RIGHT)
                        .speakerId("SPEAKER_B")
                        .text(result.getText().trim())
                        .startTime(result.getBeginTime())
                        .endTime(result.getEndTime())
                        .isFinal(result.getIsFinal())
                        .segmentNumber(result.getSegmentNumber())
                        .build());
            }
        }

        // 按开始时间排序
        allSegments.sort(Comparator.comparing(TimelineSegment::getStartTime));
        
        log.debug("合并片段完成: 总片段数={}", allSegments.size());
        return allSegments;
    }

    /**
     * 过滤过短的片段
     */
    private List<TimelineSegment> filterShortSegments(
            List<TimelineSegment> segments, 
            DialogueRecognitionRequest.DialogueAlignmentConfig config) {

        return segments.stream()
                .filter(segment -> {
                    long duration = segment.getEndTime() - segment.getStartTime();
                    return duration >= config.getMinSpeechDuration();
                })
                .collect(Collectors.toList());
    }

    /**
     * 识别对话轮次
     */
    private List<DialogueRecognitionResult.DialogueTurn> identifyDialogueTurns(
            List<TimelineSegment> segments,
            DialogueRecognitionRequest.DialogueAlignmentConfig config) {

        List<DialogueRecognitionResult.DialogueTurn> turns = new ArrayList<>();
        int turnNumber = 1;

        for (int i = 0; i < segments.size(); i++) {
            TimelineSegment currentSegment = segments.get(i);
            
            // 检查是否需要与前一个片段合并（同一说话人连续说话）
            if (i > 0 && shouldMergeWithPrevious(segments.get(i - 1), currentSegment, config)) {
                // 合并到上一个轮次
                DialogueRecognitionResult.DialogueTurn lastTurn = turns.get(turns.size() - 1);
                lastTurn = mergeSegmentToTurn(lastTurn, currentSegment);
                turns.set(turns.size() - 1, lastTurn);
            } else {
                // 创建新的对话轮次
                DialogueRecognitionResult.DialogueTurn turn = DialogueRecognitionResult.DialogueTurn.builder()
                        .turnNumber(turnNumber++)
                        .speakerId(currentSegment.getSpeakerId())
                        .channel(currentSegment.getChannel())
                        .text(currentSegment.getText())
                        .startTime(currentSegment.getStartTime())
                        .endTime(currentSegment.getEndTime())
                        .duration(currentSegment.getEndTime() - currentSegment.getStartTime())
                        .confidence(0.8) // 默认置信度，可以根据实际情况调整
                        .turnType(DialogueRecognitionResult.TurnType.STATEMENT) // 默认类型，后续分析
                        .wordTimings(new ArrayList<>()) // 词级时间信息可以后续补充
                        .build();
                turns.add(turn);
            }
        }

        return turns;
    }

    /**
     * 判断是否应该与前一个片段合并
     */
    private boolean shouldMergeWithPrevious(
            TimelineSegment previous, 
            TimelineSegment current,
            DialogueRecognitionRequest.DialogueAlignmentConfig config) {

        // 必须是同一个说话人
        if (!previous.getSpeakerId().equals(current.getSpeakerId())) {
            return false;
        }

        // 时间间隔不能太长
        long gap = current.getStartTime() - previous.getEndTime();
        return gap <= config.getChannelSwitchTolerance();
    }

    /**
     * 将片段合并到对话轮次
     */
    private DialogueRecognitionResult.DialogueTurn mergeSegmentToTurn(
            DialogueRecognitionResult.DialogueTurn turn, 
            TimelineSegment segment) {

        return DialogueRecognitionResult.DialogueTurn.builder()
                .turnNumber(turn.getTurnNumber())
                .speakerId(turn.getSpeakerId())
                .channel(turn.getChannel())
                .text(turn.getText() + " " + segment.getText())
                .startTime(turn.getStartTime())
                .endTime(segment.getEndTime())
                .duration(segment.getEndTime() - turn.getStartTime())
                .confidence(turn.getConfidence())
                .turnType(turn.getTurnType())
                .wordTimings(turn.getWordTimings())
                .build();
    }

    /**
     * 分析对话模式并标记问答类型
     */
    private List<DialogueRecognitionResult.DialogueTurn> analyzeDialoguePattern(
            List<DialogueRecognitionResult.DialogueTurn> turns,
            DialogueRecognitionRequest.DialogueAlignmentConfig config) {

        for (int i = 0; i < turns.size(); i++) {
            DialogueRecognitionResult.DialogueTurn turn = turns.get(i);
            DialogueRecognitionResult.TurnType turnType = analyzeTurnType(turn, i, turns, config);
            
            // 更新轮次类型
            turns.set(i, DialogueRecognitionResult.DialogueTurn.builder()
                    .turnNumber(turn.getTurnNumber())
                    .speakerId(turn.getSpeakerId())
                    .channel(turn.getChannel())
                    .text(turn.getText())
                    .startTime(turn.getStartTime())
                    .endTime(turn.getEndTime())
                    .duration(turn.getDuration())
                    .confidence(turn.getConfidence())
                    .turnType(turnType)
                    .wordTimings(turn.getWordTimings())
                    .build());
        }

        return turns;
    }

    /**
     * 分析单个轮次的类型
     */
    private DialogueRecognitionResult.TurnType analyzeTurnType(
            DialogueRecognitionResult.DialogueTurn turn,
            int index,
            List<DialogueRecognitionResult.DialogueTurn> allTurns,
            DialogueRecognitionRequest.DialogueAlignmentConfig config) {

        String text = turn.getText().toLowerCase();

        // 简单的问句识别
        if (text.contains("？") || text.contains("?") || 
            text.contains("什么") || text.contains("怎么") || 
            text.contains("为什么") || text.contains("哪里") ||
            text.contains("谁") || text.contains("多少")) {
            return DialogueRecognitionResult.TurnType.QUESTION;
        }

        // 如果前一个轮次是问题且说话人不同，则可能是回答
        if (index > 0) {
            DialogueRecognitionResult.DialogueTurn previousTurn = allTurns.get(index - 1);
            if (previousTurn.getTurnType() == DialogueRecognitionResult.TurnType.QUESTION &&
                !previousTurn.getSpeakerId().equals(turn.getSpeakerId())) {
                
                long responseTime = turn.getStartTime() - previousTurn.getEndTime();
                if (responseTime >= config.getMinResponseInterval() && 
                    responseTime <= config.getMaxResponseInterval()) {
                    return DialogueRecognitionResult.TurnType.ANSWER;
                }
            }
        }

        return DialogueRecognitionResult.TurnType.STATEMENT;
    }

    /**
     * 时间轴片段内部类
     */
    private static class TimelineSegment {
        private final DialogueRecognitionResult.ChannelType channel;
        private final String speakerId;
        private final String text;
        private final Long startTime;
        private final Long endTime;
        private final Boolean isFinal;
        private final Integer segmentNumber;

        private TimelineSegment(Builder builder) {
            this.channel = builder.channel;
            this.speakerId = builder.speakerId;
            this.text = builder.text;
            this.startTime = builder.startTime;
            this.endTime = builder.endTime;
            this.isFinal = builder.isFinal;
            this.segmentNumber = builder.segmentNumber;
        }

        public static Builder builder() {
            return new Builder();
        }

        // Getters
        public DialogueRecognitionResult.ChannelType getChannel() { return channel; }
        public String getSpeakerId() { return speakerId; }
        public String getText() { return text; }
        public Long getStartTime() { return startTime; }
        public Long getEndTime() { return endTime; }
        public Boolean getIsFinal() { return isFinal; }
        public Integer getSegmentNumber() { return segmentNumber; }

        public static class Builder {
            private DialogueRecognitionResult.ChannelType channel;
            private String speakerId;
            private String text;
            private Long startTime;
            private Long endTime;
            private Boolean isFinal;
            private Integer segmentNumber;

            public Builder channel(DialogueRecognitionResult.ChannelType channel) {
                this.channel = channel;
                return this;
            }

            public Builder speakerId(String speakerId) {
                this.speakerId = speakerId;
                return this;
            }

            public Builder text(String text) {
                this.text = text;
                return this;
            }

            public Builder startTime(Long startTime) {
                this.startTime = startTime;
                return this;
            }

            public Builder endTime(Long endTime) {
                this.endTime = endTime;
                return this;
            }

            public Builder isFinal(Boolean isFinal) {
                this.isFinal = isFinal;
                return this;
            }

            public Builder segmentNumber(Integer segmentNumber) {
                this.segmentNumber = segmentNumber;
                return this;
            }

            public TimelineSegment build() {
                return new TimelineSegment(this);
            }
        }
    }
}
