package com.iflytek.aicc.flow.infrastructure.rpc.iat.dialogue.dto;

import com.iflytek.aicc.flow.infrastructure.rpc.iat.dto.IatRecognitionResult;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 对话识别专用响应VO
 * 扩展了标准IAT响应，增加了词级时间信息和声道标识
 *
 * <AUTHOR>
 */
@Data
@Builder
public class DialogueIatResponseVO {

    /** 会话ID */
    private String sessionId;

    /** 声道类型标识 */
    private String channelType;

    /** 错误码 */
    private Integer errCode;

    /** 错误信息 */
    private String errMsg;

    /** 是否结束 */
    private Boolean finished;

    /** 纯文本结果 */
    private String text;

    /** 时间戳 */
    private Long timestamp;

    /** 音频段信息，从1开始 */
    private Integer segmentNumber;

    /** 音频段开始时间 */
    private Long beginTime;

    /** 音频段结束时间 */
    private Long endTime;

    /** 音频段能量 */
    private Double energy;

    /** 是否为中间结果 */
    private Boolean isIntermediate;

    /** 词级时间信息 */
    private List<WordTimingInfo> wordTimings;

    /** 原始识别结果 */
    private IatRecognitionResult rawResult;
}
