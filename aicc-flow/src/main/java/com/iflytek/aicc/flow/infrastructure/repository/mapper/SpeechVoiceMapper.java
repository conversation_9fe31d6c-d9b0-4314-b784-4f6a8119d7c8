package com.iflytek.aicc.flow.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.aicc.flow.infrastructure.repository.dataobject.SpeechVoiceDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/10/18 11:13
 */
@Mapper
public interface SpeechVoiceMapper extends BaseMapper<SpeechVoiceDO> {

    /**
     * 添加数据(保存之前根据话术ID删除以前关联的数据)
     *
     * @param speechNlpInterfaces   接口列表
     */
    void batchSave(@Param("list") List<SpeechVoiceDO> speechNlpInterfaces);
}
